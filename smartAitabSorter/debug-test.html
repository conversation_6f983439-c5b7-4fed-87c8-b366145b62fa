<!DOCTYPE html>
<html>
<head>
    <title>Extension Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>AI Tab Organizer Debug Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Basic Extension Loading</h3>
        <button onclick="testExtensionLoaded()">Test Extension Loaded</button>
        <div id="test1-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 2: Chrome APIs Available</h3>
        <button onclick="testChromeAPIs()">Test Chrome APIs</button>
        <div id="test2-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h3>Test 3: OpenAI API Test</h3>
        <input type="password" id="testApiKey" placeholder="Enter your OpenAI API key">
        <button onclick="testOpenAIAPI()">Test OpenAI API</button>
        <div id="test3-result" class="log"></div>
    </div>

    <script>
        function log(testId, message) {
            const element = document.getElementById(testId + '-result');
            element.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '<br>';
        }

        function testExtensionLoaded() {
            log('test1', 'Testing if extension context is available...');
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                log('test1', '✅ Chrome extension context is available');
                log('test1', 'Extension ID: ' + chrome.runtime.id);
            } else {
                log('test1', '❌ Chrome extension context not available');
            }
        }

        function testChromeAPIs() {
            log('test2', 'Testing Chrome APIs...');
            
            const apis = ['tabs', 'windows', 'storage'];
            apis.forEach(api => {
                if (chrome[api]) {
                    log('test2', `✅ chrome.${api} is available`);
                } else {
                    log('test2', `❌ chrome.${api} is NOT available`);
                }
            });
        }

        async function testOpenAIAPI() {
            const apiKey = document.getElementById('testApiKey').value.trim();
            if (!apiKey) {
                log('test3', '❌ Please enter an API key');
                return;
            }

            log('test3', 'Testing OpenAI API connection...');
            
            try {
                const response = await fetch('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`
                    },
                    body: JSON.stringify({
                        model: 'gpt-4o-mini',
                        messages: [
                            {
                                role: 'user',
                                content: 'Say "API test successful" if you can read this.'
                            }
                        ],
                        max_tokens: 50
                    })
                });

                log('test3', `Response status: ${response.status}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    log('test3', `❌ API Error: ${JSON.stringify(errorData)}`);
                    return;
                }

                const data = await response.json();
                log('test3', `✅ API Response: ${data.choices[0].message.content}`);
                
            } catch (error) {
                log('test3', `❌ Network Error: ${error.message}`);
            }
        }
    </script>
</body>
</html>
