
INSTALLATION STEPS:

1. Create folder structure:
   tab-organizer-extension/
   ├── manifest.json
   ├── popup.html  
   ├── popup.js
   └── background.js

2. Save each code section to its respective file

3. Install in Chrome:
   - Open Chrome and go to chrome://extensions/
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked" 
   - Select your tab-organizer-extension folder
   - Extension icon will appear in toolbar

4. Configure API:
   - Click the extension icon
   - Enter your OpenAI API key (get one at https://platform.openai.com/api-keys)
   - Choose your preferred model
   - Click "Save Configuration"

5. Use the extension:
   - "AI Smart Organize" - Uses AI to categorize tabs intelligently
   - "Simple Domain Group" - Groups by website domain
   - "Export URLs" - Downloads all URLs as text file

GETTING AN OPENAI API KEY:
1. Go to https://platform.openai.com/
2. Sign up/login
3. Go to API Keys section
4. Create a new secret key
5. Copy and paste into the extension

The AI will analyze your tab titles and URLs to create meaningful categories like:
- "Work & Productivity" 
- "Learning & Documentation"
- "Social Media & Communication"
- "Shopping & E-commerce"
- etc.
*/