document.addEventListener('DOMContentLoaded', function() {
    const aiOrganizeBtn = document.getElementById('aiOrganizeBtn');
    const simpleOrganizeBtn = document.getElementById('simpleOrganizeBtn');
    const exportBtn = document.getElementById('exportBtn');
    const saveConfigBtn = document.getElementById('saveConfig');
    const status = document.getElementById('status');
    const apiKeyInput = document.getElementById('apiKey');
    const modelSelect = document.getElementById('model');
  
    // Load saved configuration
    chrome.storage.sync.get(['apiKey', 'model'], function(result) {
      if (result.apiKey) {
        apiKeyInput.value = result.apiKey;
      }
      if (result.model) {
        modelSelect.value = result.model;
      }
    });
  
    saveConfigBtn.addEventListener('click', function() {
      const apiKey = apiKeyInput.value.trim();
      if (!apiKey) {
        showStatus('Please enter your OpenAI API key', 'error');
        return;
      }
      
      chrome.storage.sync.set({
        apiKey: apiKey,
        model: modelSelect.value
      }, function() {
        showStatus('Configuration saved!', 'success');
      });
    });
  
    aiOrganizeBtn.addEventListener('click', function() {
      console.log('AI Organize button clicked');
      chrome.storage.sync.get(['apiKey', 'model'], function(result) {
        console.log('Retrieved config:', { hasApiKey: !!result.apiKey, model: result.model });
        if (!result.apiKey) {
          showStatus('Please configure your API key first', 'error');
          return;
        }

        chrome.tabs.query({}, function(tabs) {
          console.log('Found tabs:', tabs.length);
          aiOrganizeTabs(tabs, result.apiKey, result.model);
        });
      });
    });
  
    simpleOrganizeBtn.addEventListener('click', function() {
      chrome.tabs.query({}, function(tabs) {
        simpleOrganizeTabs(tabs);
      });
    });
  
    exportBtn.addEventListener('click', function() {
      chrome.tabs.query({}, function(tabs) {
        exportUrls(tabs);
      });
    });
  
    async function aiOrganizeTabs(tabs, apiKey, model) {
      console.log('aiOrganizeTabs called with:', { tabCount: tabs.length, hasApiKey: !!apiKey, model });
      showStatus('🤖 AI is analyzing your tabs...', 'info');

      try {
        // Remove duplicates
        const uniqueTabs = removeDuplicates(tabs);
        console.log('Unique tabs:', uniqueTabs.length);
        showStatus(`Found ${uniqueTabs.length} unique tabs. Getting AI suggestions...`, 'info');

        // Prepare data for AI analysis
        const tabData = uniqueTabs.map(tab => ({
          title: tab.title,
          url: tab.url,
          domain: getDomain(tab.url)
        }));

        console.log('Tab data prepared:', tabData);

        // Get AI categorization
        const categories = await getAiCategorization(tabData, apiKey, model);
        console.log('AI categorization result:', categories);

        if (categories.error) {
          showStatus(`AI Error: ${categories.error}`, 'error');
          return;
        }

        // Create windows based on AI suggestions
        await createWindowsFromCategories(categories, uniqueTabs);
        showStatus(`✅ Created ${Object.keys(categories).length} organized windows!`, 'success');

      } catch (error) {
        console.error('Error in aiOrganizeTabs:', error);
        showStatus(`Error: ${error.message}`, 'error');
      }
    }
  
    async function getAiCategorization(tabData, apiKey, model) {
      console.log('getAiCategorization called with:', { tabDataLength: tabData.length, model });
      const prompt = `Analyze these browser tabs and organize them into logical, meaningful categories.
      Create category names that are professional and descriptive. Aim for 3-8 categories maximum.

      Tabs to categorize:
      ${tabData.map((tab, i) => `${i + 1}. ${tab.title} (${tab.domain})`).join('\n')}

      Respond with ONLY a JSON object where keys are category names and values are arrays of tab indices (1-based).
      Example: {"Work & Productivity": [1, 3, 5], "Social Media": [2, 4], "Learning": [6, 7, 8]}`;

      console.log('Sending prompt to OpenAI:', prompt);

      try {
        console.log('Making API request to OpenAI...');
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify({
            model: model || 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are a helpful assistant that organizes browser tabs into logical categories. Always respond with valid JSON only.'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            max_tokens: 1000,
            temperature: 0.3
          })
        });
  
        console.log('API response status:', response.status);
        if (!response.ok) {
          const errorData = await response.json();
          console.error('API error response:', errorData);
          throw new Error(errorData.error?.message || `HTTP ${response.status}`);
        }

        const data = await response.json();
        console.log('API response data:', data);
        const content = data.choices[0].message.content.trim();
        console.log('AI response content:', content);

        // Parse JSON response
        const categories = JSON.parse(content);
        console.log('Parsed categories:', categories);

        // Convert 1-based indices to 0-based and validate
        const validatedCategories = {};
        for (const [categoryName, indices] of Object.entries(categories)) {
          validatedCategories[categoryName] = indices
            .map(i => i - 1)
            .filter(i => i >= 0 && i < tabData.length);
        }

        console.log('Validated categories:', validatedCategories);
        return validatedCategories;
        
      } catch (error) {
        console.error('AI API Error:', error);
        return { error: error.message };
      }
    }
  
    async function createWindowsFromCategories(categories, tabs) {
      console.log('createWindowsFromCategories called with:', { categories, tabCount: tabs.length });
      let isFirstWindow = true;

      for (const [categoryName, tabIndices] of Object.entries(categories)) {
        console.log(`Processing category: ${categoryName}, indices:`, tabIndices);
        if (tabIndices.length === 0) continue;

        const tabsForCategory = tabIndices.map(i => tabs[i]).filter(Boolean);
        console.log(`Tabs for category ${categoryName}:`, tabsForCategory.length);

        if (tabsForCategory.length > 0) {
          console.log(`Creating window for category: ${categoryName}`);
          // Create new window with first tab
          const window = await chrome.windows.create({
            url: tabsForCategory[0].url,
            focused: isFirstWindow
          });
          
          // Add remaining tabs to the window
          for (let i = 1; i < tabsForCategory.length; i++) {
            await chrome.tabs.create({
              windowId: window.id,
              url: tabsForCategory[i].url,
              active: false
            });
          }
          
          // Add a tab with category name as title (optional)
          await chrome.tabs.create({
            windowId: window.id,
            url: `data:text/html,<html><head><title>${categoryName}</title></head><body><h1>📁 ${categoryName}</h1><p>This window contains tabs organized by AI.</p></body></html>`,
            active: false,
            index: 0
          });
          
          isFirstWindow = false;
          
          // Small delay to avoid overwhelming the browser
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }
  
    function simpleOrganizeTabs(tabs) {
      showStatus('📁 Organizing by domain...', 'info');
      
      const uniqueTabs = removeDuplicates(tabs);
      const domainGroups = {};
      
      uniqueTabs.forEach(tab => {
        const domain = getDomain(tab.url);
        if (!domainGroups[domain]) {
          domainGroups[domain] = [];
        }
        domainGroups[domain].push(tab);
      });
  
      // Create new windows for each domain group
      let windowsCreated = 0;
      Object.keys(domainGroups).forEach((domain, index) => {
        const tabsForDomain = domainGroups[domain];
        if (tabsForDomain.length > 1) {
          chrome.windows.create({
            url: tabsForDomain[0].url,
            focused: index === 0
          }, function(window) {
            for (let i = 1; i < tabsForDomain.length; i++) {
              chrome.tabs.create({
                windowId: window.id,
                url: tabsForDomain[i].url,
                active: false
              });
            }
          });
          windowsCreated++;
        }
      });
  
      showStatus(`✅ Created ${windowsCreated} domain-based windows`, 'success');
    }
  
    function exportUrls(tabs) {
      const uniqueTabs = removeDuplicates(tabs);
      const urlList = uniqueTabs.map(tab => `${tab.title}\n${tab.url}`).join('\n\n');
      
      const blob = new Blob([urlList], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      
      chrome.downloads.download({
        url: url,
        filename: `chrome-tabs-export-${new Date().toISOString().split('T')[0]}.txt`
      });
      
      showStatus(`💾 Exported ${uniqueTabs.length} unique tabs`, 'success');
    }
  
    function removeDuplicates(tabs) {
      const seen = new Set();
      return tabs.filter(tab => {
        if (seen.has(tab.url)) {
          return false;
        }
        seen.add(tab.url);
        return true;
      });
    }
  
    function getDomain(url) {
      try {
        const hostname = new URL(url).hostname;
        return hostname.replace(/^www\./, '');
      } catch {
        return 'other';
      }
    }
  
    function showStatus(message, type) {
      status.textContent = message;
      status.className = type;
      
      if (type === 'success') {
        setTimeout(() => {
          status.textContent = '';
          status.className = '';
        }, 3000);
      }
    }
  });
  