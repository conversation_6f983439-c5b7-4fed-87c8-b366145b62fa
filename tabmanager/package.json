{"name": "tab-manager", "version": "1.0.0", "description": "A standalone desktop app to manage Chrome and Edge browser tabs", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "keywords": ["tab-manager", "browser", "chrome", "edge", "electron"], "author": "Tab Manager", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"node-json-rpc": "^0.0.8"}, "build": {"appId": "com.tabmanager.app", "productName": "Tab Manager", "directories": {"output": "dist"}, "files": ["src/**/*", "extensions/**/*", "native-host/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}}