document.addEventListener('DOMContentLoaded', async () => {
    const statusDiv = document.getElementById('status');
    const connectionStatus = document.getElementById('connectionStatus');
    const tabCount = document.getElementById('tabCount');
    const refreshBtn = document.getElementById('refreshBtn');

    async function updateStatus() {
        try {
            // Get current tabs
            const tabs = await chrome.tabs.query({});
            tabCount.textContent = tabs.length;

            // Check if background script is connected
            const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
            
            if (response && response.connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = 'Connected to Tab Manager';
                connectionStatus.textContent = 'Connected';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = 'Disconnected from Tab Manager';
                connectionStatus.textContent = 'Disconnected';
            }
        } catch (error) {
            console.error('Error updating status:', error);
            statusDiv.className = 'status disconnected';
            statusDiv.textContent = 'Error checking connection';
            connectionStatus.textContent = 'Error';
        }
    }

    refreshBtn.addEventListener('click', async () => {
        refreshBtn.textContent = 'Refreshing...';
        refreshBtn.disabled = true;
        
        try {
            await chrome.runtime.sendMessage({ action: 'refresh' });
            await updateStatus();
        } catch (error) {
            console.error('Error refreshing:', error);
        } finally {
            refreshBtn.textContent = 'Refresh Connection';
            refreshBtn.disabled = false;
        }
    });

    // Initial status update
    await updateStatus();
    
    // Update status every 5 seconds
    setInterval(updateStatus, 5000);
});
