// Background script for Tab Manager Bridge extension
class TabManagerBridge {
  constructor() {
    this.nativePort = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.setupNativeMessaging();
    this.setupTabListeners();
  }

  setupNativeMessaging() {
    try {
      // Connect to native messaging host
      this.nativePort = chrome.runtime.connectNative('com.tabmanager.host');
      
      this.nativePort.onMessage.addListener((message) => {
        this.handleNativeMessage(message);
      });

      this.nativePort.onDisconnect.addListener(() => {
        const error = chrome.runtime.lastError;
        console.log('Native messaging disconnected:', error ? error.message : 'No error');
        this.isConnected = false;
        this.nativePort = null;
        this.reconnectAttempts++;

        // Try to reconnect, but with exponential backoff and max attempts
        if (this.reconnectAttempts <= this.maxReconnectAttempts) {
          const delay = Math.min(3000 * Math.pow(2, this.reconnectAttempts - 1), 30000);
          setTimeout(() => {
            console.log(`Attempting to reconnect to native host... (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            this.setupNativeMessaging();
          }, delay);
        } else {
          console.log('Max reconnection attempts reached. Stopping reconnection attempts.');
        }
      });

      this.isConnected = true;
      this.reconnectAttempts = 0; // Reset reconnection attempts on successful connection
      console.log('Native messaging connected');

      // Send initial tab data after a short delay
      setTimeout(() => {
        // Check if port is still connected before sending
        if (this.isConnected && this.nativePort) {
          // First send a simple test message
          this.nativePort.postMessage({
            action: 'test',
            message: 'Hello from Chrome extension'
          });

          // Then send tab data
          this.sendAllTabs();
        }
      }, 500);
      
    } catch (error) {
      console.error('Failed to connect to native messaging:', error);
      this.isConnected = false;
    }
  }

  setupTabListeners() {
    // Listen for tab changes
    chrome.tabs.onCreated.addListener(() => {
      this.sendAllTabs();
    });

    chrome.tabs.onRemoved.addListener(() => {
      this.sendAllTabs();
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.title || changeInfo.url) {
        this.sendAllTabs();
      }
    });

    chrome.windows.onCreated.addListener(() => {
      this.sendAllTabs();
    });

    chrome.windows.onRemoved.addListener(() => {
      this.sendAllTabs();
    });
  }

  async handleNativeMessage(message) {
    console.log('Received native message:', message);

    switch (message.action) {
      case 'getTabs':
        await this.sendAllTabs();
        break;

      case 'closeTab':
        console.log(`Received close request for tab ${message.tabId}`);
        await this.closeTab(message.tabId);
        break;

      case 'focusTab':
        await this.focusTab(message.tabId);
        break;

      case 'tabsUpdateAck':
        console.log(`Native host acknowledged ${message.count} tabs for ${message.browser}`);
        break;

      case 'tabsBatchAck':
        console.log(`Native host acknowledged batch ${message.batchInfo.current}/${message.batchInfo.total} (${message.totalReceived} total tabs)`);
        break;

      case 'testResponse':
        console.log('Test response from native host:', message.message);
        break;

      default:
        console.warn('Unknown action:', message.action);
    }
  }

  async sendAllTabs() {
    if (!this.isConnected || !this.nativePort) {
      return;
    }

    try {
      const tabs = await chrome.tabs.query({});
      console.log(`Processing ${tabs.length} tabs...`);

      // If we have too many tabs, send them in batches
      if (tabs.length > 100) {
        console.log(`Large number of tabs (${tabs.length}), sending in batches...`);
        await this.sendTabsInBatches(tabs);
        return;
      }

      const tabData = tabs.map(tab => ({
        id: `chrome-${tab.id}`,
        originalId: tab.id,
        title: (tab.title || 'Untitled').substring(0, 100), // Limit title length
        url: (tab.url || '').substring(0, 200), // Limit URL length
        favicon: tab.favIconUrl || '',
        browser: 'chrome',
        windowId: tab.windowId,
        active: tab.active,
        pinned: tab.pinned
      }));

      const message = {
        action: 'tabsUpdate',
        browser: 'chrome',
        tabs: tabData
      };

      this.nativePort.postMessage(message);
      console.log(`Sent ${tabData.length} Chrome tabs to native host`);

    } catch (error) {
      console.error('Error sending tabs:', error);
    }
  }

  async sendTabsInBatches(tabs) {
    const batchSize = 50; // Send 50 tabs at a time
    const totalBatches = Math.ceil(tabs.length / batchSize);

    for (let i = 0; i < totalBatches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, tabs.length);
      const batchTabs = tabs.slice(start, end);

      const tabData = batchTabs.map(tab => ({
        id: `chrome-${tab.id}`,
        originalId: tab.id,
        title: (tab.title || 'Untitled').substring(0, 100),
        url: (tab.url || '').substring(0, 200),
        favicon: tab.favIconUrl || '',
        browser: 'chrome',
        windowId: tab.windowId,
        active: tab.active,
        pinned: tab.pinned
      }));

      const message = {
        action: 'tabsBatch',
        browser: 'chrome',
        tabs: tabData,
        batchInfo: {
          current: i + 1,
          total: totalBatches,
          isLast: i === totalBatches - 1
        }
      };

      this.nativePort.postMessage(message);
      console.log(`Sent batch ${i + 1}/${totalBatches} (${tabData.length} tabs)`);

      // Small delay between batches to prevent overwhelming the native host
      if (i < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  }

  async closeTab(tabId) {
    try {
      // Extract the original Chrome tab ID
      const originalId = parseInt(tabId.replace('chrome-', ''));
      console.log(`Attempting to close Chrome tab ${originalId} (from ${tabId})`);

      // Check if tab exists before trying to close it
      try {
        await chrome.tabs.get(originalId);
        await chrome.tabs.remove(originalId);
        console.log(`✅ Successfully closed tab ${originalId}`);
      } catch (tabError) {
        console.log(`Tab ${originalId} may already be closed or doesn't exist:`, tabError.message);
      }

      // Send updated tab list after a short delay
      setTimeout(() => {
        this.sendAllTabs();
      }, 500);

    } catch (error) {
      console.error('Error closing tab:', error);
    }
  }

  async focusTab(tabId) {
    try {
      const originalId = parseInt(tabId.replace('chrome-', ''));
      const tab = await chrome.tabs.get(originalId);
      
      // Focus the window and activate the tab
      await chrome.windows.update(tab.windowId, { focused: true });
      await chrome.tabs.update(originalId, { active: true });
      
      console.log(`Focused tab ${originalId}`);
      
    } catch (error) {
      console.error('Error focusing tab:', error);
    }
  }
}

// Initialize the bridge when the extension starts
const tabManagerBridge = new TabManagerBridge();

// Handle extension icon click
chrome.action.onClicked.addListener(() => {
  // Could open the desktop app or show status
  console.log('Tab Manager Bridge clicked');
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Received message from popup:', message);

  switch (message.action) {
    case 'getStatus':
      sendResponse({ connected: tabManagerBridge.isConnected });
      break;

    case 'refresh':
      tabManagerBridge.sendAllTabs();
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ error: 'Unknown action' });
  }

  return true; // Keep the message channel open for async response
});
