<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            text-align: center;
            font-size: 14px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
        .button {
            width: 100%;
            padding: 10px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>🗂️ Tab Manager Bridge</h3>
    </div>
    
    <div id="status" class="status disconnected">
        Checking connection...
    </div>
    
    <div class="info">
        This extension bridges your Chrome tabs with the Tab Manager desktop application.
        <br><br>
        <strong>Status:</strong> <span id="connectionStatus">Checking...</span>
        <br>
        <strong>Tabs:</strong> <span id="tabCount">0</span>
    </div>
    
    <button class="button" id="refreshBtn">Refresh Connection</button>
    
    <script src="popup.js"></script>
</body>
</html>
