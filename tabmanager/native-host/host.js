#!/opt/homebrew/bin/node

// Native messaging host for Tab Manager
const fs = require('fs');
const path = require('path');

class NativeMessagingHost {
  constructor() {
    this.tabs = new Map(); // Store tabs by browser type
    this.electronApp = null; // Reference to Electron app process
    this.setupStdio();
    this.startListening();
  }

  setupStdio() {
    // Set up binary mode for stdin/stdout - don't set encoding for binary data
    process.stdin.resume();
    process.stdout._flush = process.stdout._flush || (() => {});
  }

  startListening() {
    let messageLength = 0;
    let messageBuffer = Buffer.alloc(0);
    let expectingLength = true;

    process.stdin.on('data', (data) => {
      messageBuffer = Buffer.concat([messageBuffer, data]);

      while (messageBuffer.length > 0) {
        if (expectingLength) {
          if (messageBuffer.length >= 4) {
            // Read the 4-byte message length (little-endian)
            messageLength = messageBuffer.readUInt32LE(0);
            messageBuffer = messageBuffer.slice(4);
            expectingLength = false;
            this.logInfo(`Expecting message of length: ${messageLength}`);
          } else {
            break; // Wait for more data
          }
        } else {
          if (messageBuffer.length >= messageLength) {
            // Read the message
            const messageStr = messageBuffer.slice(0, messageLength).toString('utf8');
            messageBuffer = messageBuffer.slice(messageLength);
            expectingLength = true;

            this.logInfo(`Received message: ${messageStr}`);
            try {
              const message = JSON.parse(messageStr);
              this.handleMessage(message);
            } catch (error) {
              this.logError('Failed to parse message:', error);
            }
          } else {
            break; // Wait for more data
          }
        }
      }
    });

    process.stdin.on('end', () => {
      this.logInfo('stdin ended');
      // Don't exit immediately, let the process stay alive
    });

    process.on('SIGINT', () => {
      this.logInfo('Received SIGINT, exiting');
      process.exit(0);
    });

    this.logInfo('Native messaging host started');
  }

  handleMessage(message) {
    this.logInfo('Received message:', JSON.stringify(message));

    switch (message.action) {
      case 'test':
        this.logInfo('Received test message:', message.message);
        this.sendMessage({
          action: 'testResponse',
          message: 'Hello back from native host!'
        });
        break;

      case 'tabsUpdate':
        this.handleTabsUpdate(message);
        break;

      case 'tabsBatch':
        this.handleTabsBatch(message);
        break;

      case 'getTabs':
        this.sendAllTabs();
        // Also send a direct response
        this.sendMessage({
          action: 'tabsResponse',
          tabs: this.getAllTabs()
        });
        break;

      case 'closeTab':
        this.handleCloseTab(message);
        break;

      case 'focusTab':
        this.handleFocusTab(message);
        break;

      default:
        this.logError('Unknown action:', message.action);
    }
  }

  handleTabsUpdate(message) {
    const { browser, tabs } = message;
    this.tabs.set(browser, tabs);

    this.logInfo(`Updated ${tabs.length} tabs for ${browser}`);

    // Send acknowledgment back to extension
    this.sendMessage({
      action: 'tabsUpdateAck',
      browser: browser,
      count: tabs.length
    });

    // Forward to Electron app if connected
    this.forwardToElectron({
      type: 'tabs-updated',
      browser,
      tabs: this.getAllTabs()
    });
  }

  handleTabsBatch(message) {
    const { browser, tabs, batchInfo } = message;

    // Initialize or get existing tabs for this browser
    let existingTabs = this.tabs.get(browser) || [];

    if (batchInfo.current === 1) {
      // First batch - reset the tabs
      existingTabs = [];
      this.logInfo(`Starting batch update for ${browser} (${batchInfo.total} batches)`);
    }

    // Add this batch of tabs
    existingTabs.push(...tabs);
    this.tabs.set(browser, existingTabs);

    this.logInfo(`Received batch ${batchInfo.current}/${batchInfo.total} for ${browser} (${tabs.length} tabs, ${existingTabs.length} total)`);

    // Send acknowledgment
    this.sendMessage({
      action: 'tabsBatchAck',
      browser: browser,
      batchInfo: batchInfo,
      totalReceived: existingTabs.length
    });

    // If this is the last batch, forward to Electron app
    if (batchInfo.isLast) {
      this.logInfo(`Completed batch update for ${browser}: ${existingTabs.length} total tabs`);
      this.forwardToElectron({
        type: 'tabs-updated',
        browser,
        tabs: this.getAllTabs()
      });
    }
  }

  handleCloseTab(message) {
    const { tabId } = message;
    
    // Determine browser from tab ID
    const browser = tabId.startsWith('chrome-') ? 'chrome' : 'edge';
    
    // Send close command back to the appropriate browser
    this.sendMessage({
      action: 'closeTab',
      tabId: tabId
    });
    
    this.logInfo(`Requested to close tab ${tabId}`);
  }

  handleFocusTab(message) {
    const { tabId } = message;
    
    // Send focus command back to the appropriate browser
    this.sendMessage({
      action: 'focusTab',
      tabId: tabId
    });
    
    this.logInfo(`Requested to focus tab ${tabId}`);
  }

  getAllTabs() {
    const allTabs = [];
    for (const [browser, tabs] of this.tabs) {
      allTabs.push(...tabs);
    }
    return allTabs;
  }

  sendAllTabs() {
    const allTabs = this.getAllTabs();
    this.forwardToElectron({
      type: 'all-tabs',
      tabs: allTabs
    });
  }

  forwardToElectron(data) {
    // In a real implementation, this would communicate with the Electron app
    // For now, we'll log it
    this.logInfo('Forwarding to Electron:', JSON.stringify(data));
  }

  sendMessage(message) {
    try {
      const messageStr = JSON.stringify(message);
      const messageBuffer = Buffer.from(messageStr, 'utf8');
      const messageLength = messageBuffer.length;

      // Write 4-byte length header (little-endian)
      const lengthBuffer = Buffer.allocUnsafe(4);
      lengthBuffer.writeUInt32LE(messageLength, 0);
      process.stdout.write(lengthBuffer);

      // Write message
      process.stdout.write(messageBuffer);

      this.logInfo('Sent message:', messageStr);
    } catch (error) {
      this.logError('Failed to send message:', error);
    }
  }

  logInfo(...args) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] INFO: ${args.join(' ')}\n`;
    fs.appendFileSync(path.join(__dirname, 'host.log'), logMessage);
  }

  logError(...args) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
    fs.appendFileSync(path.join(__dirname, 'host.log'), logMessage);
  }
}

// Start the native messaging host
new NativeMessagingHost();
