#!/usr/bin/env node

// Native messaging host for Tab Manager
const fs = require('fs');
const path = require('path');

class NativeMessagingHost {
  constructor() {
    this.tabs = new Map(); // Store tabs by browser type
    this.electronApp = null; // Reference to Electron app process
    this.setupStdio();
    this.startListening();
  }

  setupStdio() {
    // Set up binary mode for stdin/stdout
    process.stdin.setEncoding('utf8');
    process.stdout.setDefaultEncoding('utf8');
    
    // Prevent Node.js from buffering
    process.stdin.resume();
    process.stdout._flush = process.stdout._flush || (() => {});
  }

  startListening() {
    let messageLength = 0;
    let messageBuffer = '';
    let expectingLength = true;

    process.stdin.on('data', (data) => {
      messageBuffer += data;

      while (messageBuffer.length > 0) {
        if (expectingLength) {
          if (messageBuffer.length >= 4) {
            // Read the 4-byte message length
            messageLength = messageBuffer.charCodeAt(0) |
                          (messageBuffer.charCodeAt(1) << 8) |
                          (messageBuffer.charCodeAt(2) << 16) |
                          (messageBuffer.charCodeAt(3) << 24);
            
            messageBuffer = messageBuffer.substring(4);
            expectingLength = false;
          } else {
            break; // Wait for more data
          }
        } else {
          if (messageBuffer.length >= messageLength) {
            // Read the message
            const messageStr = messageBuffer.substring(0, messageLength);
            messageBuffer = messageBuffer.substring(messageLength);
            expectingLength = true;

            try {
              const message = JSON.parse(messageStr);
              this.handleMessage(message);
            } catch (error) {
              this.logError('Failed to parse message:', error);
            }
          } else {
            break; // Wait for more data
          }
        }
      }
    });

    process.stdin.on('end', () => {
      this.logInfo('stdin ended');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      this.logInfo('Received SIGINT, exiting');
      process.exit(0);
    });

    this.logInfo('Native messaging host started');
  }

  handleMessage(message) {
    this.logInfo('Received message:', JSON.stringify(message));

    switch (message.action) {
      case 'tabsUpdate':
        this.handleTabsUpdate(message);
        break;
      
      case 'getTabs':
        this.sendAllTabs();
        break;
        
      case 'closeTab':
        this.handleCloseTab(message);
        break;
        
      case 'focusTab':
        this.handleFocusTab(message);
        break;
        
      default:
        this.logError('Unknown action:', message.action);
    }
  }

  handleTabsUpdate(message) {
    const { browser, tabs } = message;
    this.tabs.set(browser, tabs);
    
    // Forward to Electron app if connected
    this.forwardToElectron({
      type: 'tabs-updated',
      browser,
      tabs: this.getAllTabs()
    });
    
    this.logInfo(`Updated ${tabs.length} tabs for ${browser}`);
  }

  handleCloseTab(message) {
    const { tabId } = message;
    
    // Determine browser from tab ID
    const browser = tabId.startsWith('chrome-') ? 'chrome' : 'edge';
    
    // Send close command back to the appropriate browser
    this.sendMessage({
      action: 'closeTab',
      tabId: tabId
    });
    
    this.logInfo(`Requested to close tab ${tabId}`);
  }

  handleFocusTab(message) {
    const { tabId } = message;
    
    // Send focus command back to the appropriate browser
    this.sendMessage({
      action: 'focusTab',
      tabId: tabId
    });
    
    this.logInfo(`Requested to focus tab ${tabId}`);
  }

  getAllTabs() {
    const allTabs = [];
    for (const [browser, tabs] of this.tabs) {
      allTabs.push(...tabs);
    }
    return allTabs;
  }

  sendAllTabs() {
    const allTabs = this.getAllTabs();
    this.forwardToElectron({
      type: 'all-tabs',
      tabs: allTabs
    });
  }

  forwardToElectron(data) {
    // In a real implementation, this would communicate with the Electron app
    // For now, we'll log it
    this.logInfo('Forwarding to Electron:', JSON.stringify(data));
  }

  sendMessage(message) {
    try {
      const messageStr = JSON.stringify(message);
      const messageLength = Buffer.byteLength(messageStr, 'utf8');
      
      // Write 4-byte length header
      const lengthBuffer = Buffer.allocUnsafe(4);
      lengthBuffer.writeUInt32LE(messageLength, 0);
      process.stdout.write(lengthBuffer);
      
      // Write message
      process.stdout.write(messageStr, 'utf8');
      
      this.logInfo('Sent message:', messageStr);
    } catch (error) {
      this.logError('Failed to send message:', error);
    }
  }

  logInfo(...args) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] INFO: ${args.join(' ')}\n`;
    fs.appendFileSync(path.join(__dirname, 'host.log'), logMessage);
  }

  logError(...args) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ERROR: ${args.join(' ')}\n`;
    fs.appendFileSync(path.join(__dirname, 'host.log'), logMessage);
  }
}

// Start the native messaging host
new NativeMessagingHost();
