{"type": "tabs-updated", "browser": "chrome", "tabs": [{"id": "chrome-220055294", "originalId": 220055294, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220055318", "originalId": 220055318, "title": "Chai.new · vibe code any agent | by Langbase", "url": "https://chai.new/", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055324", "originalId": 220055324, "title": "Ma<PERSON>wl<PERSON>", "url": "https://www.magnowlia.com/", "favicon": "https://www.magnowlia.com/wp-content/uploads/2024/04/cropped-Magnowlia_logo_slack_blue_darksand-1-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055330", "originalId": 220055330, "title": "LoRA", "url": "https://huggingface.co/docs/diffusers/main/en/training/lora", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055333", "originalId": 220055333, "title": "HEIMOANA Current position (Unknown, MMSI *********) - VesselFinder", "url": "https://www.vesselfinder.com/?mmsi=*********", "favicon": "https://www.vesselfinder.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055336", "originalId": 220055336, "title": "UAE Public Holidays 2025 - <PERSON>ly Ann<PERSON>", "url": "https://www.bayzat.com/blog/public-holidays-uae/#:~:text=Eid%2Dal%2DAdha%20%E2%80%93%206%2D8%2C%20June%2C%202025&text=The%20UAE%20Moon%20Sighting%20Committee,will%20extend%20till%20June%208.", "favicon": "https://www.bayzat.com/blog/wp-content/uploads/2022/05/cropped-icon-48x48-1.webp", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Dubai to Berlin | Google Flights", "url": "https://www.google.com/travel/flights/search?tfs=CBwQAhopEgoyMDI1LTA2LTA1ag0IAhIJL20vMDFmMDhycgwIAxIIL20vMDE1NnEaKRIKMjAyNS0wNi0wOWoMCAMSCC9tLzAxNTZxcg0IAhIJL20vMDFmMDhyQAFIAXABggELCP___________wGYAQE", "favicon": "https://www.gstatic.com/images/branding/product/2x/travel_flights_48dp.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "(4) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ag-ui-protocol/ag-ui: AG-UI: the Agent-User Interaction Protocol. Bring Agents into Frontend Applica", "url": "https://github.com/ag-ui-protocol/ag-ui", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Proton Mail: Sign-in", "url": "https://account.proton.me/mail", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=6iavtfjbw24", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Proton Mail: Sign-in", "url": "https://account.proton.me/mail", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=6iavtfjbw24", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Oracle Cloud Free Tier Signup", "url": "https://signup.cloud.oracle.com/?verify_email=eyJhbGciOiJIUzI1NiJ9.eyJjYXB0Y2hhSnd0VG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlKOS5leUp6ZFdJaU9pSnZZMmt0YzJsbmJuVndJaXdpYVhOeklqb2lhSFIwY0hNNkx5OXphV2R1ZFhBdWIzS", "favicon": "https://signup.cloud.oracle.com/oraclefavicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Oracle Cloud Free Tier Signup", "url": "https://signup.cloud.oracle.com/?verify_email=eyJhbGciOiJIUzI1NiJ9.eyJjYXB0Y2hhSnd0VG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlKOS5leUp6ZFdJaU9pSnZZMmt0YzJsbmJuVndJaXdpYVhOeklqb2lhSFIwY0hNNkx5OXphV2R1ZFhBdWIzS", "favicon": "https://signup.cloud.oracle.com/oraclefavicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055366", "originalId": 220055366, "title": "What is the Databricks CLI? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/dev-tools/cli", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055369", "originalId": 220055369, "title": "New Tab", "url": "chrome://new-tab-page/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055370", "originalId": 220055370, "title": "GCPing.com", "url": "https://gcping.com/", "favicon": "https://gcping.com/icon.662224a4.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055373", "originalId": 220055373, "title": "cloudping.info", "url": "https://www.cloudping.info/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055376", "originalId": 220055376, "title": "Pricing | Prefect Cloud", "url": "https://www.prefect.io/pricing", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055382", "originalId": 220055382, "title": "ishanExtreme/a2a_mcp-example: An example showing how A2A and MCP can be used together", "url": "https://github.com/ishanExtreme/a2a_mcp-example", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055391", "originalId": 220055391, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/oauth/code/success?app=claude-code", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055394", "originalId": 220055394, "title": "sda814a.evoshosting.com", "url": "http://sda814a.evoshosting.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055397", "originalId": 220055397, "title": "Power BI: Pricing Plan | Microsoft Power Platform", "url": "https://www.microsoft.com/en/power-platform/products/power-bi/pricing?market=af#tabs-pill-bar-ocbbe94_tab1", "favicon": "https://www.microsoft.com/favicon.ico?v2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055400", "originalId": 220055400, "title": "Microsoft Fabric - Pricing | Microsoft Azure", "url": "https://azure.microsoft.com/en-us/pricing/details/microsoft-fabric/", "favicon": "https://azure.microsoft.com/cdn-classic/cvt-bb8a1cf1009a5b5c7760c9ad231a12f2327b0f120d671060a326ad4c7c919be9/images/icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055403", "originalId": 220055403, "title": "Outlook for macOS MCP Server by <PERSON> | PulseMCP", "url": "https://www.pulsemcp.com/servers/syedazharmbnr1-outlook-for-macos", "favicon": "https://www.pulsemcp.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055406", "originalId": 220055406, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/3d265b3f-8939-491d-a215-a45bf06c8729/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055409", "originalId": 220055409, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/2dc7e96c-c083-419d-948e-f24c7f8d9883/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055412", "originalId": 220055412, "title": "Kilo Code Documentation | Kilo Code Docs", "url": "https://kilocode.ai/docs/", "favicon": "https://kilocode.ai/docs/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055415", "originalId": 220055415, "title": "Create tables  |  Dataform  |  Google Cloud", "url": "https://cloud.google.com/dataform/docs/create-tables", "favicon": "https://www.gstatic.com/devrel-devsite/prod/v31bf0d5ece3babea9777b807f088a03e9bb2225d007f11b8410e9c896eb213a6/cloud/images/favicons/onecloud/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055418", "originalId": 220055418, "title": "Top Open Source ETL Frameworks in 2025 + Alternatives | Integrate.io", "url": "https://www.integrate.io/blog/open-source-etl-frameworks-revolutionizing-data-integration/", "favicon": "https://www.integrate.io/blog/assets/favicon-7b3fac9c5056e956d9a9c853a88d9979229875e342663df97bd54b2eecee2827.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055421", "originalId": 220055421, "title": "Dremio Apache Iceberg Solutions and Capabilities", "url": "https://www.dremio.com/platform/apache-iceberg/", "favicon": "https://www.dremio.com//wp-content/themes/subsurface/assets/_corp/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055424", "originalId": 220055424, "title": "Domain", "url": "https://ap.www.namecheap.com/domains/domaincontrolpanel/ecoptimum.tech/domain", "favicon": "https://www.namecheap.com/assets/img/nc-icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055427", "originalId": 220055427, "title": "Customer Stories | Hightouch", "url": "https://hightouch.com/customers", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055433", "originalId": 220055433, "title": "Qualtrics S&R - Google Search", "url": "https://www.google.com/search?q=Qualtrics+S%26R&oq=Qualtrics+S%26R&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBBzM0M2owajGoAgCwAgA&sourceid=chrome&ie=UTF-8", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055436", "originalId": 220055436, "title": "Integrations | Hightouch", "url": "https://hightouch.com/integrations?type=destination", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055439", "originalId": 220055439, "title": "About MetricFlow | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/build/about-metricflow", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055442", "originalId": 220055442, "title": "Braze User Guide", "url": "https://www.braze.com/docs/user_guide/introduction", "favicon": "https://www.braze.com/docs/assets/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055445", "originalId": 220055445, "title": "Base44", "url": "https://app.base44.com/", "favicon": "https://app.base44.com/logo_v3.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055448", "originalId": 220055448, "title": "plum financial assistant - Google Search", "url": "https://www.google.com/search?q=plum+financial+assistant&sca_esv=eee3cc9543ede721&sxsrf=AE3TifM0RiKIxIPidc-tRkxYjpYS68_uMw%3A1750414507130&ei=qzRVaN_NB8mKi-gP9eC9yAQ&ved=0ahUKEwifz6CX4v-NAxVJxQIHHXVwD", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055451", "originalId": 220055451, "title": "Dittofeed | Open-source Customer Engagement for Automated Messaging", "url": "https://www.dittofeed.com/", "favicon": "https://cdn.prod.website-files.com/664393f5bd2b0c6d4fdf5437/6660af223efe69a76107dc35_Dittofeed_newLogo_graphicOnly_favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055454", "originalId": 220055454, "title": "(1) New Messages!", "url": "https://www.beyondtrust.com/brand?utm_source=google&utm_medium=cpc&utm_campaign=9336390457&utm_content=************&utm_term=beyond%20trust&gad_source=1&gad_campaignid=9336390457&gbraid=0AAAAACjgS9pCF", "favicon": "https://www.beyondtrust.com/favicon.png?v=5", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055457", "originalId": 220055457, "title": "Microsoft Teams Meets Databricks Genie API: A Complete Setup Guide | by <PERSON> | Medium", "url": "https://medium.com/@ryan-bates/microsoft-teams-meets-databricks-genie-api-a-complete-setup-guide-81f629ace634", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Claude <PERSON> overview - Anthropic", "url": "https://docs.anthropic.com/en/docs/claude-code/overview", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/anthropic/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/login?selectAccount=true&returnTo=%2Foauth%2Fauthorize%3Fcode%3Dtrue%26client_id%3D9d1c250a-e61b-44d9-88ed-5944d1962f5e%26response_type%3Dcode%26redirect_uri%3Dhttp%253A%", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "LLM Guardrail for Employees | Secure AI Workflow | WitnessAI", "url": "https://witness.ai/for-employees/", "favicon": "https://witness.ai/favicon.ico?v=2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ideas - Google Sheets", "url": "https://docs.google.com/spreadsheets/d/1JO2j6-oeRez-w-NUyYAYXzIP_gbJrX5ZwiM_50WE8-U/edit?gid=0#gid=0", "favicon": "https://ssl.gstatic.com/docs/spreadsheets/spreadsheets_2023q4.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "what is the valuation of", "url": "https://www.perplexity.ai/search/what-is-the-valuation-of-7A3oVzgBQ9qbh4ua_vy80g", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055484", "originalId": 220055484, "title": "what are the critical financial numbers for www.zand.ae/", "url": "https://www.perplexity.ai/search/what-are-the-critical-financia-PqF_27O.Sp6T3eJoUarbfQ", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055490", "originalId": 220055490, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058050", "originalId": 220058050, "title": "Inbox (73) - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055499", "originalId": 220055499, "title": "XLSTAT | Statistical Software for Excel", "url": "https://www.xlstat.com/", "favicon": "https://cdn.xlstat.com/dist/assets/img/favicon.svg?v=af378", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055502", "originalId": 220055502, "title": "ChatLLM Teams", "url": "https://apps.abacus.ai/chatllm/?appId=73da811f2", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055505", "originalId": 220055505, "title": "ChatLLM - AI Chat And Agents on the App Store", "url": "https://apps.apple.com/us/app/chatllm-ai-chat-and-agents/id6502844681", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055508", "originalId": 220055508, "title": "Abacus.AI - Project - test_1 - Datasets", "url": "https://abacus.ai/app/dataset_list/1503e157a4", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "New Tab", "url": "chrome://new-tab-page/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Abacus.AI - CodeLLM", "url": "https://codellm.abacus.ai/", "favicon": "https://codellm.abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "app.augmentcode.com/account/subscription", "url": "https://app.augmentcode.com/account/subscription", "favicon": "https://app.augmentcode.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ChatLLM Teams", "url": "https://apps.abacus.ai/chatllm/?appId=appllm_engineer", "favicon": "https://abacus.ai/static/imgs/hp_chat_llm.webp", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Abacus.AI - MCP Servers How-To", "url": "https://abacus.ai/help/howTo/chatllm/mcp_servers_how_to", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "MCP Servers", "url": "https://mcp.so/", "favicon": "https://mcp.so/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Abacus.AI - Project - test_1 - Dashboard", "url": "https://abacus.ai/app/projects/1503e157a4?doUpload=true", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055533", "originalId": 220055533, "title": "Tab Manager - sort, group and more - Chrome Web Store", "url": "https://chromewebstore.google.com/detail/tab-manager-sort-group-an/hjobkgjcapaiplinfaeipeefmjcdepml?hl=en", "favicon": "https://ssl.gstatic.com/chrome/webstore/images/icon_48px.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057666", "originalId": 220057666, "title": "Pricing - Augment Code", "url": "https://www.augmentcode.com/pricing", "favicon": "https://www.augmentcode.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055539", "originalId": 220055539, "title": "New Tab", "url": "chrome://newtab/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058089", "originalId": 220058089, "title": "Install and Upgrade - HVR 6 | Fivetran Documentation", "url": "https://fivetran.com/docs/hvr6/install-and-upgrade", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057660", "originalId": 220057660, "title": "Extensions", "url": "chrome://extensions/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058053", "originalId": 220058053, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055548", "originalId": 220055548, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055551", "originalId": 220055551, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055554", "originalId": 220055554, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055557", "originalId": 220055557, "title": "OSS Specifications", "url": "https://www.mage.ai/oss", "favicon": "https://framerusercontent.com/images/ofrGuj4GtrMewS5s195d0pFIkGY.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055560", "originalId": 220055560, "title": "apache/beam: Apache Beam is a unified programming model for Batch and Streaming data processing.", "url": "https://github.com/apache/beam", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055563", "originalId": 220055563, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055566", "originalId": 220055566, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055569", "originalId": 220055569, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055572", "originalId": 220055572, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055575", "originalId": 220055575, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055578", "originalId": 220055578, "title": "clerk.copilotkit.ai", "url": "https://clerk.copilotkit.ai/v1/client/handshake?redirect_url=https%3A%2F%2Fcloud.copilotkit.ai%2Fonboarding", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055581", "originalId": 220055581, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055584", "originalId": 220055584, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055587", "originalId": 220055587, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055590", "originalId": 220055590, "title": "Getting Started: Deploying | Next.js", "url": "https://nextjs.org/docs/app/getting-started/deploying", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055593", "originalId": 220055593, "title": "supabase-orange-river | tom's projects | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/sql/9c078490-4c2a-48c9-945c-48816bec561a", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055596", "originalId": 220055596, "title": "glossary | Railway", "url": "https://railway.com/project/************************************/service/1fa739df-d725-4abb-9068-74c083b27998/settings?environmentId=23f319c3-14e1-469b-a35c-46a809dda1d0#networking-public", "favicon": "https://railway.com/favicons/favicon-light.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055599", "originalId": 220055599, "title": "Corporate Glossary", "url": "https://glossary-production.up.railway.app/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055602", "originalId": 220055602, "title": "alt144/glossary at dev-initial", "url": "https://github.com/alt144/glossary/tree/dev-initial", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055605", "originalId": 220055605, "title": "Augment Code Billing", "url": "https://billing.augmentcode.com/p/session/live_YWNjdF8xUXB6TUFBbXY5Z1Y5NnR1LF9TRFlLWVVMNzdVWGxHMjh0TzczVXVzc2lPdDVZc3lT0100qddA0uZq", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Blockchain Auditor", "url": "http://localhost:8501/", "favicon": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>%F0%9F%93%8A</text></svg>", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment Signup - Create a free account | Twilio Segment", "url": "https://segment.com/signup/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Verify Email - Segment", "url": "https://app.segment.com/verify?response=eyJ1c2VyIjp7ImlkIjoid0Y0NmJOblF2YVFxd01ZTk5GdGZBUiIsIm5hbWUiOiJUaG9tYXMgQ291cm9ubsOpIiwiZW1haWwiOiJ0aG9tYXMuY291cm9ubmVAYmV5b25kLm9uZSIsImNyZWF0ZWQiOiIyMDI1LTA1", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "clevertap - Google Search", "url": "https://www.google.com/search?q=clevertap&oq=clevertap&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBCDEzNzJqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055620", "originalId": 220055620, "title": "(12) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055623", "originalId": 220055623, "title": "diffbot/Llama-3.3-Diffbot-Small-XL-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.3-Diffbot-Small-XL-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055626", "originalId": 220055626, "title": "diffbot/Llama-3.1-Diffbot-Small-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.1-Diffbot-Small-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055629", "originalId": 220055629, "title": "Integration Health - Segment", "url": "https://app.segment.com/beyond-thomas-couronne/integration-health", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055632", "originalId": 220055632, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055635", "originalId": 220055635, "title": "doc-rag-support-agent-1385 - alt144", "url": "https://chai.new/alt144/doc-rag-support-agent-1385", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055638", "originalId": 220055638, "title": "API Keys | Exa API", "url": "https://dashboard.exa.ai/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055641", "originalId": 220055641, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055644", "originalId": 220055644, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055647", "originalId": 220055647, "title": "SQLite Home Page", "url": "https://sqlite.org/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055650", "originalId": 220055650, "title": "alt144/glossary at dev", "url": "https://github.com/alt144/glossary/tree/dev", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055653", "originalId": 220055653, "title": "🚀 Airflow 3.0 Just Dropped: Explore What's New | DataTalks.Club Weekly - <EMAIL> ", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/50+_2x.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055656", "originalId": 220055656, "title": "Qwen3-30B-A3B is on another level (Appreciation Post) : r/LocalLLaMA", "url": "https://www.reddit.com/r/LocalLLaMA/comments/1kbkv2d/qwen330ba3b_is_on_another_level_appreciation_post/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbkv2d&p", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055659", "originalId": 220055659, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055662", "originalId": 220055662, "title": "API | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/api", "favicon": "https://supabase.com/dashboard/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055665", "originalId": 220055665, "title": "Local Development & CLI | Supabase Docs", "url": "https://supabase.com/docs/guides/local-development", "favicon": "https://supabase.com/docs/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055668", "originalId": 220055668, "title": "(3) Regardez France Inter - YouTube", "url": "https://www.youtube.com/watch?v=ACPnVZDNjJs", "favicon": "https://www.youtube.com/s/desktop/2253fa3d/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055671", "originalId": 220055671, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055674", "originalId": 220055674, "title": "dbt Power User", "url": "https://docs.myaltimate.com/", "favicon": "https://docs.myaltimate.com/assets/images/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055677", "originalId": 220055677, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055680", "originalId": 220055680, "title": "Command Line Interface (CLI)", "url": "https://code.visualstudio.com/docs/configure/command-line", "favicon": "https://code.visualstudio.com/assets/favicon.ico", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220058056", "originalId": 220058056, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055688", "originalId": 220055688, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055691", "originalId": 220055691, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055694", "originalId": 220055694, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055697", "originalId": 220055697, "title": "OSS Specifications", "url": "https://www.mage.ai/oss", "favicon": "https://framerusercontent.com/images/ofrGuj4GtrMewS5s195d0pFIkGY.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055700", "originalId": 220055700, "title": "apache/beam: Apache Beam is a unified programming model for Batch and Streaming data processing.", "url": "https://github.com/apache/beam", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055703", "originalId": 220055703, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055706", "originalId": 220055706, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055709", "originalId": 220055709, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055712", "originalId": 220055712, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055715", "originalId": 220055715, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055718", "originalId": 220055718, "title": "clerk.copilotkit.ai", "url": "https://clerk.copilotkit.ai/v1/client/handshake?redirect_url=https%3A%2F%2Fcloud.copilotkit.ai%2Fonboarding", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055721", "originalId": 220055721, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055724", "originalId": 220055724, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055727", "originalId": 220055727, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055730", "originalId": 220055730, "title": "Getting Started: Deploying | Next.js", "url": "https://nextjs.org/docs/app/getting-started/deploying", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055733", "originalId": 220055733, "title": "supabase-orange-river | tom's projects | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/sql/9c078490-4c2a-48c9-945c-48816bec561a", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055736", "originalId": 220055736, "title": "glossary | Railway", "url": "https://railway.com/project/************************************/service/1fa739df-d725-4abb-9068-74c083b27998/settings?environmentId=23f319c3-14e1-469b-a35c-46a809dda1d0#networking-public", "favicon": "https://railway.com/favicons/favicon-light.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055739", "originalId": 220055739, "title": "Corporate Glossary", "url": "https://glossary-production.up.railway.app/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055742", "originalId": 220055742, "title": "alt144/glossary at dev-initial", "url": "https://github.com/alt144/glossary/tree/dev-initial", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055745", "originalId": 220055745, "title": "Augment Code Billing", "url": "https://billing.augmentcode.com/p/session/live_YWNjdF8xUXB6TUFBbXY5Z1Y5NnR1LF9TRFlLWVVMNzdVWGxHMjh0TzczVXVzc2lPdDVZc3lT0100qddA0uZq", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Blockchain Auditor", "url": "http://localhost:8501/", "favicon": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>%F0%9F%93%8A</text></svg>", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment Signup - Create a free account | Twilio Segment", "url": "https://segment.com/signup/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fverify%3Fresponse%3DeyJ1c2VyIjp7ImlkIjoid0Y0NmJOblF2YVFxd01ZTk5GdGZBUiIsIm5hbWUiOiJUaG9tYXMgQ291cm9ubsOpIiwiZW1haWwiOiJ0aG9tYXMuY291cm9ubmVAYmV5b25kLm9uZSIsIm", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "clevertap - Google Search", "url": "https://www.google.com/search?q=clevertap&oq=clevertap&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBCDEzNzJqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055760", "originalId": 220055760, "title": "(4) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055763", "originalId": 220055763, "title": "diffbot/Llama-3.3-Diffbot-Small-XL-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.3-Diffbot-Small-XL-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055766", "originalId": 220055766, "title": "diffbot/Llama-3.1-Diffbot-Small-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.1-Diffbot-Small-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055769", "originalId": 220055769, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fbeyond-thomas-couronne%2Fintegration-health", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055772", "originalId": 220055772, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055775", "originalId": 220055775, "title": "doc-rag-support-agent-1385 - alt144", "url": "https://chai.new/alt144/doc-rag-support-agent-1385?v=1", "favicon": "https://command.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055778", "originalId": 220055778, "title": "API Keys | Exa API", "url": "https://dashboard.exa.ai/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055781", "originalId": 220055781, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055784", "originalId": 220055784, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055787", "originalId": 220055787, "title": "SQLite Home Page", "url": "https://sqlite.org/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055790", "originalId": 220055790, "title": "alt144/glossary at dev", "url": "https://github.com/alt144/glossary/tree/dev", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055793", "originalId": 220055793, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055796", "originalId": 220055796, "title": "API | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/api", "favicon": "https://supabase.com/dashboard/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055799", "originalId": 220055799, "title": "Local Development & CLI | Supabase Docs", "url": "https://supabase.com/docs/guides/local-development", "favicon": "https://supabase.com/docs/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055802", "originalId": 220055802, "title": "(3) Regardez France Inter - YouTube", "url": "https://www.youtube.com/watch?v=ACPnVZDNjJs", "favicon": "https://www.youtube.com/s/desktop/2253fa3d/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055805", "originalId": 220055805, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055808", "originalId": 220055808, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055811", "originalId": 220055811, "title": "Page 1 | Untitled | Editor | Retool", "url": "https://corona.retool.com/editor/baa74f3e-5267-11f0-add6-337947c70add/Untitled/page1", "favicon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8yXzI2KSI+CjxtYXNrIGlkPSJtYXNrMF8yXzI2IiBzdHlsZT0ibWFzay10eXBlOmx1bWluYW5jZSIgbWFza1VuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeD0iMCIgeT0iMCIgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiPgo8cGF0aCBkPSJNMTI4IDBIMFYxMjhIMTI4VjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L21hc2s+CjxnIG1hc2s9InVybCgjbWFzazBfMl8yNikiPgo8cGF0aCBkPSJNODAuNjQxNiAxNS42NjY3QzgwLjY0MTYgMTIuOTA1MiA3OC40MDMgMTAuNjY2NiA3NS42NDE2IDEwLjY2NjZIMTguOTc0QzE2LjIxMjYgMTAuNjY2NiAxMy45NzQgMTIuOTA1MiAxMy45NzQgMTUuNjY2N1YzOS4wMDA0QzEzLjk3NCA0MS43NjE4IDE2LjIxMjYgNDQuMDAwNCAxOC45NzQgNDQuMDAwNEg3NS42NDE2Qzc4LjQwMyA0NC4wMDA0IDgwLjY0MTYgNDYuMjM5IDgwLjY0MTYgNDkuMDAwNVY1NS42NjczQzgwLjY0MTYgNTguNDI4NyA3OC40MDMgNjAuNjY3NCA3NS42NDE2IDYwLjY2NzRIMzguOTc0M0MzNi4yMTI5IDYwLjY2NzQgMzMuOTc0MyA2Mi45MDYgMzMuOTc0MyA2NS42Njc0Vjg5LjAwMTFDMzMuOTc0MyA5MS43NjI2IDM2LjIxMjkgOTQuMDAxMiAzOC45NzQzIDk0LjAwMTJINzUuNjQxNkM3OC40MDMgOTQuMDAxMiA4MC42NDE2IDk2LjIzOTggODAuNjQxNiA5OS4wMDEzVjExMi4zMzVDODAuNjQxNiAxMTUuMDk2IDgyLjg4MDMgMTE3LjMzNSA4NS42NDE3IDExNy4zMzVIMTA4Ljk3NUMxMTEuNzM3IDExNy4zMzUgMTEzLjk3NSAxMTUuMDk2IDExMy45NzUgMTEyLjMzNVY4OS4wMDExQzExMy45NzUgODYuMjM5NyAxMTEuNzM3IDg0LjAwMSAxMDguOTc1IDg0LjAwMUg4NS42NDE3QzgyLjg4MDMgODQuMDAxIDgwLjY0MTYgODEuNzYyNCA4MC42NDE2IDc5LjAwMVY3Mi4zMzQyQzgwLjY0MTYgNjkuNTcyNyA4Mi44ODAzIDY3LjMzNDEgODUuNjQxNyA2Ny4zMzQxSDEwOC45NzVDMTExLjczNyA2Ny4zMzQxIDExMy45NzUgNjUuMDk1NSAxMTMuOTc1IDYyLjMzNDFWMzkuMDAwNEMxMTMuOTc1IDM2LjIzODkgMTExLjczNyAzNC4wMDAzIDEwOC45NzUgMzQuMDAwM0g4NS42NDE3QzgyLjg4MDMgMzQuMDAwMyA4MC42NDE2IDMxLjc2MTcgODAuNjQxNiAyOS4wMDAyVjE1LjY2NjdaIiBmaWxsPSIjOTk5OTk5Ii8+CjwvZz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8yXzI2Ij4KPHJlY3Qgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055814", "originalId": 220055814, "title": "Watch Best New Movies & Shows from Biggest Studios | OSN+", "url": "https://osnplus.com/en-ae?gad_source=1&gad_campaignid=***********&gbraid=0AAAAABnYLV5z0fQSHtIr0bHl-FtL4oe7M&gclid=CjwKCAjw3_PCBhA2EiwAkH_j4hMF4mhWhPnYZGYFq95esj3UQmD1aDjzi1WP1Y5sxKodfouisetZoRoCnYAQAv", "favicon": "https://osnplus.com/favicon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055817", "originalId": 220055817, "title": "Fabric – your self-organizing workspace and file explorer", "url": "https://fabric.so/welcome", "favicon": "https://framerusercontent.com/images/I44tR0tcOIpWRJxbR6x608smGE.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055820", "originalId": 220055820, "title": "Introducing the Weaviate Query Agent | Weaviate", "url": "https://weaviate.io/blog/query-agent?utm_source=channels&utm_medium=vs_social&utm_campaign=agents&utm_content=honeypot_post_680309399", "favicon": "https://weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055823", "originalId": 220055823, "title": "Menlo Research", "url": "https://menlo.ai/", "favicon": "https://menlo.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055826", "originalId": 220055826, "title": "3Blue1Brown - But what is a GPT? Visual intro to Transformers | Deep learning, chapter 5", "url": "https://www.3blue1brown.com/lessons/gpt", "favicon": "https://www.3blue1brown.com/favicons/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055829", "originalId": 220055829, "title": "Why MCP Won - Latent.Space", "url": "https://www.latent.space/p/why-mcp-won", "favicon": "https://substackcdn.com/image/fetch/$s_!J-V9!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ffeff84b7-b3cb-4c54-95d5-bd2a208f3f6d%2Ffavicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055832", "originalId": 220055832, "title": "Amazon Web Services (AWS)", "url": "https://profile.aws.amazon.com/#/profile/details", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055835", "originalId": 220055835, "title": "(87) Inbox | <EMAIL> | Proton Mail", "url": "https://mail.proton.me/u/4/inbox", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=b6cgradae1v", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055838", "originalId": 220055838, "title": "<PERSON> | The AI Software Engineer", "url": "https://devin.ai/", "favicon": "https://devin.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220057669", "originalId": 220057669, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055846", "originalId": 220055846, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055849", "originalId": 220055849, "title": "HazyResearch/minions: Big & Small LLMs working together", "url": "https://github.com/hazyResearch/minions/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055852", "originalId": 220055852, "title": "Automate business processes with AI | Lleverage", "url": "https://www.lleverage.ai/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055855", "originalId": 220055855, "title": "Introducing uv: Next-Gen Python Package Manager | by <PERSON> | Medium", "url": "https://codemaker2016.medium.com/introducing-uv-next-gen-python-package-manager-b78ad39c95d7#8902", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055858", "originalId": 220055858, "title": "Motiff: AI-powered Professional Interface Design Tool", "url": "https://www.motiff.com/?ref", "favicon": "https://www.motiff.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055861", "originalId": 220055861, "title": "Genspark Agents", "url": "https://www.genspark.ai/agents?type=phone_call", "favicon": "https://www.genspark.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055864", "originalId": 220055864, "title": "Zep - AI Agent Memory", "url": "https://www.getzep.com/", "favicon": "https://cdn.prod.website-files.com/660b1c076d13b7e2967a499d/660c6dc329d8b16a8468f5ba_Asset%2017.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055867", "originalId": 220055867, "title": "Chai.new · vibe code any agent | by Langbase", "url": "https://chai.new/", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055870", "originalId": 220055870, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055873", "originalId": 220055873, "title": "Ma<PERSON>wl<PERSON>", "url": "https://www.magnowlia.com/", "favicon": "https://www.magnowlia.com/wp-content/uploads/2024/04/cropped-Magnowlia_logo_slack_blue_darksand-1-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055876", "originalId": 220055876, "title": "thomascouronne - Home", "url": "https://lightning.ai/thomascouronne/home", "favicon": "https://lightning.ai/favicon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055879", "originalId": 220055879, "title": "LoRA", "url": "https://huggingface.co/docs/diffusers/main/en/training/lora", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055882", "originalId": 220055882, "title": "ag-ui-protocol/ag-ui: AG-UI: the Agent-User Interaction Protocol. Bring Agents into Frontend Applica", "url": "https://github.com/ag-ui-protocol/ag-ui", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055885", "originalId": 220055885, "title": "Notion Mail", "url": "https://www.notion.com/product/mail", "favicon": "https://www.notion.com/front-static/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055888", "originalId": 220055888, "title": "mcp-course (Hugging Face MCP Course)", "url": "https://huggingface.co/mcp-course", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055891", "originalId": 220055891, "title": "Pythonic, Modern Workflow Orchestration For Resilient Data Platforms | Prefect", "url": "https://www.prefect.io/", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055894", "originalId": 220055894, "title": "Coding-Crashkurse/A2A-LangGraph", "url": "https://github.com/Coding-Crashkurse/A2A-LangGraph", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055897", "originalId": 220055897, "title": "ishanExtreme/a2a_mcp-example: An example showing how A2A and MCP can be used together", "url": "https://github.com/ishanExtreme/a2a_mcp-example", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055900", "originalId": 220055900, "title": "Tsadoq/a2a-mcp-tutorial: A tutorial on how to use Model Context Protocol by Anthropic and Agent2Agen", "url": "https://github.com/Tsadoq/a2a-mcp-tutorial", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055903", "originalId": 220055903, "title": "Interactive Budgeting - There is 1 AI tool For That", "url": "https://theresanaiforthat.com/interactive-budgeting/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055906", "originalId": 220055906, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/oauth/code/success?app=claude-code", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055909", "originalId": 220055909, "title": "sda814a.evoshosting.com", "url": "http://sda814a.evoshosting.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055912", "originalId": 220055912, "title": "Power BI: Pricing Plan | Microsoft Power Platform", "url": "https://www.microsoft.com/en/power-platform/products/power-bi/pricing?market=af#tabs-pill-bar-ocbbe94_tab1", "favicon": "https://www.microsoft.com/favicon.ico?v2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055915", "originalId": 220055915, "title": "Microsoft Fabric - Pricing | Microsoft Azure", "url": "https://azure.microsoft.com/en-us/pricing/details/microsoft-fabric/", "favicon": "https://azure.microsoft.com/cdn-classic/cvt-bb8a1cf1009a5b5c7760c9ad231a12f2327b0f120d671060a326ad4c7c919be9/images/icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055918", "originalId": 220055918, "title": "Outlook for macOS MCP Server by <PERSON> | PulseMCP", "url": "https://www.pulsemcp.com/servers/syedazharmbnr1-outlook-for-macos", "favicon": "https://www.pulsemcp.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055921", "originalId": 220055921, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/3d265b3f-8939-491d-a215-a45bf06c8729/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055924", "originalId": 220055924, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/2dc7e96c-c083-419d-948e-f24c7f8d9883/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055927", "originalId": 220055927, "title": "Kilo Code Documentation | Kilo Code Docs", "url": "https://kilocode.ai/docs/", "favicon": "https://kilocode.ai/docs/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055930", "originalId": 220055930, "title": "Create tables  |  Dataform  |  Google Cloud", "url": "https://cloud.google.com/dataform/docs/create-tables", "favicon": "https://www.gstatic.com/devrel-devsite/prod/v31bf0d5ece3babea9777b807f088a03e9bb2225d007f11b8410e9c896eb213a6/cloud/images/favicons/onecloud/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055933", "originalId": 220055933, "title": "Top Open Source ETL Frameworks in 2025 + Alternatives | Integrate.io", "url": "https://www.integrate.io/blog/open-source-etl-frameworks-revolutionizing-data-integration/", "favicon": "https://www.integrate.io/blog/assets/favicon-7b3fac9c5056e956d9a9c853a88d9979229875e342663df97bd54b2eecee2827.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055936", "originalId": 220055936, "title": "Dremio Apache Iceberg Solutions and Capabilities", "url": "https://www.dremio.com/platform/apache-iceberg/", "favicon": "https://www.dremio.com//wp-content/themes/subsurface/assets/_corp/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055939", "originalId": 220055939, "title": "Domain", "url": "https://ap.www.namecheap.com/domains/domaincontrolpanel/ecoptimum.tech/domain", "favicon": "https://www.namecheap.com/assets/img/nc-icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055942", "originalId": 220055942, "title": "Customer Stories | Hightouch", "url": "https://hightouch.com/customers", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055945", "originalId": 220055945, "title": "Perplexity", "url": "https://www.perplexity.ai/?login-source=oneTapHome&login-new=false", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055948", "originalId": 220055948, "title": "Integrations | Hightouch", "url": "https://hightouch.com/integrations?type=destination", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055951", "originalId": 220055951, "title": "About MetricFlow | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/build/about-metricflow", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055954", "originalId": 220055954, "title": "Braze User Guide", "url": "https://www.braze.com/docs/user_guide/introduction", "favicon": "https://www.braze.com/docs/assets/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055957", "originalId": 220055957, "title": "Base44", "url": "https://app.base44.com/", "favicon": "https://app.base44.com/logo_v3.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055960", "originalId": 220055960, "title": "Plum 101: Your Questions Answered", "url": "https://blog.withplum.com/plum-user-questions-answered/", "favicon": "https://blog.withplum.com/content/images/size/w256h256/2019/07/img_plum_avatar.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055963", "originalId": 220055963, "title": "Dittofeed | Open-source Customer Engagement for Automated Messaging", "url": "https://www.dittofeed.com/", "favicon": "https://cdn.prod.website-files.com/664393f5bd2b0c6d4fdf5437/6660af223efe69a76107dc35_Dittofeed_newLogo_graphicOnly_favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055966", "originalId": 220055966, "title": "(1) New Messages!", "url": "https://www.beyondtrust.com/brand?utm_source=google&utm_medium=cpc&utm_campaign=9336390457&utm_content=************&utm_term=beyond%20trust&gad_source=1&gad_campaignid=9336390457&gbraid=0AAAAACjgS9pCF", "favicon": "https://www.beyondtrust.com/favicon.png?v=5", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055969", "originalId": 220055969, "title": "Microsoft Teams Meets Databricks Genie API: A Complete Setup Guide | by <PERSON> | Medium", "url": "https://medium.com/@ryan-bates/microsoft-teams-meets-databricks-genie-api-a-complete-setup-guide-81f629ace634", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055972", "originalId": 220055972, "title": "Predictive Analytics Software For Humans | Pecan AI", "url": "https://www.pecan.ai/home-2-1/", "favicon": "https://www.pecan.ai/wp-content/uploads/2023/11/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Claude <PERSON> overview - Anthropic", "url": "https://docs.anthropic.com/en/docs/claude-code/overview", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/anthropic/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/login?selectAccount=true&returnTo=%2Foauth%2Fauthorize%3Fcode%3Dtrue%26client_id%3D9d1c250a-e61b-44d9-88ed-5944d1962f5e%26response_type%3Dcode%26redirect_uri%3Dhttp%253A%", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Aider-AI/aider: aider is AI pair programming in your terminal", "url": "https://github.com/Aider-AI/aider", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "LLM Guardrail for Employees | Secure AI Workflow | WitnessAI", "url": "https://witness.ai/for-employees/", "favicon": "https://witness.ai/favicon.ico?v=2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ideas - Google Sheets", "url": "https://docs.google.com/spreadsheets/d/1JO2j6-oeRez-w-NUyYAYXzIP_gbJrX5ZwiM_50WE8-U/edit?gid=0#gid=0", "favicon": "https://ssl.gstatic.com/docs/spreadsheets/spreadsheets_2023q4.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055990", "originalId": 220055990, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/?r=0", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055993", "originalId": 220055993, "title": "what is the valuation of", "url": "https://www.perplexity.ai/search/what-is-the-valuation-of-7A3oVzgBQ9qbh4ua_vy80g", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055996", "originalId": 220055996, "title": "what are the critical financial numbers for www.zand.ae/", "url": "https://www.perplexity.ai/search/what-are-the-critical-financia-PqF_27O.Sp6T3eJoUarbfQ", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220055999", "originalId": 220055999, "title": "OpenInterpreter/open-interpreter: A natural language interface for computers", "url": "https://github.com/OpenInterpreter/open-interpreter", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056002", "originalId": 220056002, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056005", "originalId": 220056005, "title": "AI APIs for I - TAAFT®", "url": "https://theresanaiforthat.com/apis/s/i/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056008", "originalId": 220056008, "title": "[FREEMOBILE] **********: Nouveau message de: +15855950645 - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220056011", "originalId": 220056011, "title": "Augment Code", "url": "https://login.augmentcode.com/u/login/identifier?state=hKFo2SBhQzVnVFdpdXVjQkdpWHh0UTNDREFnbVlRdFR5MGRNU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDBOYnc5SnhaUWwybm5rV1MyZzZ5WG50enFoQ1dmWkpDo2NpZNkgd2xMVFZXR0RmS", "favicon": "https://www.augmentcode.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056014", "originalId": 220056014, "title": "MCP Servers", "url": "https://mcp.so/", "favicon": "https://mcp.so/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056017", "originalId": 220056017, "title": "Separate1", "url": "https://cdn.prod.website-files.com/6537bd39ea748ca6a0e62198/67f8209f32d9a9a52fdf5971_Brochure.pdf", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056020", "originalId": 220056020, "title": "Opera Neon | Agentic AI browser | Opera", "url": "https://www.operaneon.com/", "favicon": "https://www.operaneon.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056023", "originalId": 220056023, "title": "Kohonen Map | Food4Rhino", "url": "https://www.food4rhino.com/en/app/kohonen-map", "favicon": "https://www.food4rhino.com/sites/default/files/public/f4r/images/icons/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057672", "originalId": 220057672, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VGFr2zAQ_Sui0LLBQtnWfcmHQZqkJVtMQp2uA3UUxVESUUcKltw1jP333Z0kR67bfQjRvfd0kk93789JYVbypH-yqcR-yxaje83Y6SmbioOs2Mc-G9bWmR2sJ9rJShROGc1yU1eFtKi19TJsNXWx3RulHeGML", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056029", "originalId": 220056029, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056032", "originalId": 220056032, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_live_editor&utm_medium=toggle#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056035", "originalId": 220056035, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNqrVkrOT0lVslJSqgUAFW4DVg", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056038", "originalId": 220056038, "title": "OneOptimizer - AI-Powered Budgeting", "url": "http://localhost:8503/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056041", "originalId": 220056041, "title": "Meeting Summaries, Transcripts, AI Notetaker & Enterprise Search | read.ai", "url": "https://www.read.ai/", "favicon": "https://cdn.prod.website-files.com/614e5e239ea0f25fe5b6a797/615f7213c6091b296dd6ebd0_ReadLogomark_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056044", "originalId": 220056044, "title": "Quickstart (with cloud resources) | Weaviate Documentation", "url": "https://docs.weaviate.io/weaviate/quickstart", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056047", "originalId": 220056047, "title": "How to install Weaviate | Weaviate Documentation", "url": "https://docs.weaviate.io/deploy/installation-guides", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056050", "originalId": 220056050, "title": "Build a Weekly AI Trend Alerter with arXiv and Weaviate | n8n workflow template", "url": "https://n8n.io/workflows/5817-build-a-weekly-ai-trend-alerter-with-arxiv-and-weaviate/", "favicon": "https://n8n.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056053", "originalId": 220056053, "title": "Quickstart (with cloud resources) | Weaviate Documentation", "url": "https://docs.weaviate.io/weaviate/quickstart", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056056", "originalId": 220056056, "title": "The AI-native database developers love | Weaviate", "url": "https://weaviate.io/", "favicon": "https://weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056059", "originalId": 220056059, "title": "The Agentic Platform for Personalization | Bloomreach", "url": "https://www.bloomreach.com/en", "favicon": "https://www.bloomreach.com/wp-content/uploads/2024/04/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058059", "originalId": 220058059, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056067", "originalId": 220056067, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056070", "originalId": 220056070, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056073", "originalId": 220056073, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056076", "originalId": 220056076, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056079", "originalId": 220056079, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056082", "originalId": 220056082, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056085", "originalId": 220056085, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056088", "originalId": 220056088, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056091", "originalId": 220056091, "title": "clerk.copilotkit.ai", "url": "https://clerk.copilotkit.ai/v1/client/handshake?redirect_url=https%3A%2F%2Fcloud.copilotkit.ai%2Fonboarding", "favicon": "", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056094", "originalId": 220056094, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056097", "originalId": 220056097, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056100", "originalId": 220056100, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056103", "originalId": 220056103, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056106", "originalId": 220056106, "title": "doc-rag-support-agent-1385 - alt144", "url": "https://chai.new/alt144/doc-rag-support-agent-1385", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056109", "originalId": 220056109, "title": "API Keys | Exa API", "url": "https://dashboard.exa.ai/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056112", "originalId": 220056112, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056115", "originalId": 220056115, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056118", "originalId": 220056118, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056121", "originalId": 220056121, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056124", "originalId": 220056124, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056130", "originalId": 220056130, "title": "Sim Studio", "url": "https://www.simstudio.ai/workspace/c3a07492-3dff-4452-9b55-c28ae3979e81/w/49504948-1390-44b1-829c-e9b4c34e0f1d", "favicon": "https://www.simstudio.ai/favicon/favicon.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056133", "originalId": 220056133, "title": "Meiro Integrations", "url": "https://www.meiro.io/integrations/databricks-lakehouse/", "favicon": "https://www.meiro.io/icon.ico?09524e12951176a5", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056136", "originalId": 220056136, "title": "Atlas | Award-Winning Decision Intelligence Platform | SourseAI", "url": "https://www.sourse.ai/atlas/", "favicon": "https://www.sourse.ai/app/uploads/2021/06/favicon.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056139", "originalId": 220056139, "title": "AI Strategy Summit | Section", "url": "https://www.sectionai.com/ai/ai-strategy-summit#rsvp", "favicon": "https://cdn.prod.website-files.com/635ffd046dcb4346779f7a91/63de80b9c6567ab3cf331221_section-logo32.png", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220057675", "originalId": 220057675, "title": "Pricing - <PERSON><PERSON>", "url": "https://kiro.dev/pricing/", "favicon": "https://kiro.dev/icon.svg?fe599162bb293ea0", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220057678", "originalId": 220057678, "title": "<PERSON>", "url": "https://claude.ai/chat/b524f539-d437-4193-8231-c76127e0c6f4", "favicon": "https://claude.ai/favicon.ico", "browser": "chrome", "windowId": 220056062, "active": false, "pinned": false}, {"id": "chrome-220056148", "originalId": 220056148, "title": "ESKADENIA Software | Customer Management", "url": "https://www.eskadenia.com/customer-management-systems?mnuId=56", "favicon": "https://www.eskadenia.com/Resources/2/eskadenia-logo-ico.png", "browser": "chrome", "windowId": 220056062, "active": true, "pinned": false}, {"id": "chrome-220058062", "originalId": 220058062, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056156", "originalId": 220056156, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056159", "originalId": 220056159, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056162", "originalId": 220056162, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056165", "originalId": 220056165, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056168", "originalId": 220056168, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056171", "originalId": 220056171, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056174", "originalId": 220056174, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056177", "originalId": 220056177, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056180", "originalId": 220056180, "title": "cloud.copilotkit.ai", "url": "https://cloud.copilotkit.ai/onboarding", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056183", "originalId": 220056183, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056186", "originalId": 220056186, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056189", "originalId": 220056189, "title": "Untitled diagram | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056192", "originalId": 220056192, "title": "glossary | Railway", "url": "https://railway.com/project/************************************/service/1fa739df-d725-4abb-9068-74c083b27998/settings?environmentId=23f319c3-14e1-469b-a35c-46a809dda1d0#networking-public", "favicon": "https://railway.com/favicons/favicon-light.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056195", "originalId": 220056195, "title": "diffbot/Llama-3.3-Diffbot-Small-XL-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.3-Diffbot-Small-XL-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056198", "originalId": 220056198, "title": "diffbot/Llama-3.1-Diffbot-Small-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.1-Diffbot-Small-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056201", "originalId": 220056201, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056204", "originalId": 220056204, "title": "Command.new · vibe code any agent | by Langbase", "url": "https://command.new/", "favicon": "https://command.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056207", "originalId": 220056207, "title": "Exa API Dashboard", "url": "https://dashboard.exa.ai/login?redirect=/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056210", "originalId": 220056210, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056213", "originalId": 220056213, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056216", "originalId": 220056216, "title": "alt144/glossary at dev", "url": "https://github.com/alt144/glossary/tree/dev", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057681", "originalId": 220057681, "title": "Enregistrez-vous sur votre vol pour Dubai - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056222", "originalId": 220056222, "title": "Prêt pour l'enregistrement ? Gagnez du temps et enregistrez-vous en ligne - KLM France", "url": "https://www.klm.fr/check-in/passenger-list?checkInGroupId=6a18e04f-7289-4f96-bedd-353db5e55d1b", "favicon": "https://www.static-kl.com/assets/kl/img/logo-48.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220058047", "originalId": 220058047, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056228", "originalId": 220056228, "title": "Shiply France > Visualiser les devis de la Livraison 7 Boxes - 1m3", "url": "https://www.shiply.com/fr/listings/bids/9JXQLXLRA/LANXDDXGQ?h=&h_uid=thom10495&view_bid=LANXDDXGQ&utm_medium=email&utm_source=transactional&utm_campaign=QuoteNotification&utm_nooveride=1&utm_content=Q", "favicon": "https://www.shiply.com/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056231", "originalId": 220056231, "title": "DHL Sendungsverfolgung | DHL", "url": "https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=JJD149990200067579691", "favicon": "https://www.dhl.de/.resources/dhl/webresources/assets/icons/favicons/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056234", "originalId": 220056234, "title": "Shiply France > My Shiply > Active Deliveries", "url": "https://www.shiply.com/fr/listings/user_active", "favicon": "https://www.shiply.com/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056237", "originalId": 220056237, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056240", "originalId": 220056240, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056243", "originalId": 220056243, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056246", "originalId": 220056246, "title": "Page 1 | Untitled | Editor | Retool", "url": "https://corona.retool.com/editor/baa74f3e-5267-11f0-add6-337947c70add/Untitled/page1", "favicon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8yXzI2KSI+CjxtYXNrIGlkPSJtYXNrMF8yXzI2IiBzdHlsZT0ibWFzay10eXBlOmx1bWluYW5jZSIgbWFza1VuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeD0iMCIgeT0iMCIgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiPgo8cGF0aCBkPSJNMTI4IDBIMFYxMjhIMTI4VjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L21hc2s+CjxnIG1hc2s9InVybCgjbWFzazBfMl8yNikiPgo8cGF0aCBkPSJNODAuNjQxNiAxNS42NjY3QzgwLjY0MTYgMTIuOTA1MiA3OC40MDMgMTAuNjY2NiA3NS42NDE2IDEwLjY2NjZIMTguOTc0QzE2LjIxMjYgMTAuNjY2NiAxMy45NzQgMTIuOTA1MiAxMy45NzQgMTUuNjY2N1YzOS4wMDA0QzEzLjk3NCA0MS43NjE4IDE2LjIxMjYgNDQuMDAwNCAxOC45NzQgNDQuMDAwNEg3NS42NDE2Qzc4LjQwMyA0NC4wMDA0IDgwLjY0MTYgNDYuMjM5IDgwLjY0MTYgNDkuMDAwNVY1NS42NjczQzgwLjY0MTYgNTguNDI4NyA3OC40MDMgNjAuNjY3NCA3NS42NDE2IDYwLjY2NzRIMzguOTc0M0MzNi4yMTI5IDYwLjY2NzQgMzMuOTc0MyA2Mi45MDYgMzMuOTc0MyA2NS42Njc0Vjg5LjAwMTFDMzMuOTc0MyA5MS43NjI2IDM2LjIxMjkgOTQuMDAxMiAzOC45NzQzIDk0LjAwMTJINzUuNjQxNkM3OC40MDMgOTQuMDAxMiA4MC42NDE2IDk2LjIzOTggODAuNjQxNiA5OS4wMDEzVjExMi4zMzVDODAuNjQxNiAxMTUuMDk2IDgyLjg4MDMgMTE3LjMzNSA4NS42NDE3IDExNy4zMzVIMTA4Ljk3NUMxMTEuNzM3IDExNy4zMzUgMTEzLjk3NSAxMTUuMDk2IDExMy45NzUgMTEyLjMzNVY4OS4wMDExQzExMy45NzUgODYuMjM5NyAxMTEuNzM3IDg0LjAwMSAxMDguOTc1IDg0LjAwMUg4NS42NDE3QzgyLjg4MDMgODQuMDAxIDgwLjY0MTYgODEuNzYyNCA4MC42NDE2IDc5LjAwMVY3Mi4zMzQyQzgwLjY0MTYgNjkuNTcyNyA4Mi44ODAzIDY3LjMzNDEgODUuNjQxNyA2Ny4zMzQxSDEwOC45NzVDMTExLjczNyA2Ny4zMzQxIDExMy45NzUgNjUuMDk1NSAxMTMuOTc1IDYyLjMzNDFWMzkuMDAwNEMxMTMuOTc1IDM2LjIzODkgMTExLjczNyAzNC4wMDAzIDEwOC45NzUgMzQuMDAwM0g4NS42NDE3QzgyLjg4MDMgMzQuMDAwMyA4MC42NDE2IDMxLjc2MTcgODAuNjQxNiAyOS4wMDAyVjE1LjY2NjdaIiBmaWxsPSIjOTk5OTk5Ii8+CjwvZz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8yXzI2Ij4KPHJlY3Qgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056249", "originalId": 220056249, "title": "Watch Best New Movies & Shows from Biggest Studios | OSN+", "url": "https://osnplus.com/en-ae?gad_source=1&gad_campaignid=***********&gbraid=0AAAAABnYLV5z0fQSHtIr0bHl-FtL4oe7M&gclid=CjwKCAjw3_PCBhA2EiwAkH_j4hMF4mhWhPnYZGYFq95esj3UQmD1aDjzi1WP1Y5sxKodfouisetZoRoCnYAQAv", "favicon": "https://osnplus.com/favicon/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056252", "originalId": 220056252, "title": "stanford-oval/storm: An LLM-powered knowledge curation system that researches a topic and generates ", "url": "https://github.com/stanford-oval/storm/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056255", "originalId": 220056255, "title": "apps.abacus.ai/chatllm/?appId=73da811f2", "url": "https://apps.abacus.ai/chatllm/?appId=73da811f2", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056258", "originalId": 220056258, "title": "Cursor - The AI Code Editor", "url": "https://cursor.com/en/agents", "favicon": "https://cursor.com/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056261", "originalId": 220056261, "title": "IP information in prompt risk for AI - IBM Documentation", "url": "https://www.ibm.com/docs/en/watsonx/saas?topic=atlas-ip-information-in-prompt", "favicon": "https://www.ibm.com/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056264", "originalId": 220056264, "title": "dittofeed/dittofeed: Open-source customer engagement. Automate transactional and marketing messages ", "url": "https://github.com/dittofeed/dittofeed", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056267", "originalId": 220056267, "title": "Semantic layer - PandasAI", "url": "https://docs.pandas-ai.com/v3/semantic-layer", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/sinaptik/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056270", "originalId": 220056270, "title": "NL Layer - PandasAI", "url": "https://docs.pandas-ai.com/v3/overview-nl", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/sinaptik/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056273", "originalId": 220056273, "title": "NL2SQL with BigQuery and Gemini | Google Cloud Blog", "url": "https://cloud.google.com/blog/products/data-analytics/nl2sql-with-bigquery-and-gemini", "favicon": "https://www.gstatic.com/cloud/images/icons/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056276", "originalId": 220056276, "title": "Overview | DB-GPT", "url": "http://docs.dbgpt.cn/docs/overview", "favicon": "http://docs.dbgpt.cn/img/eosphoros.jpeg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056279", "originalId": 220056279, "title": "eosphoros-ai/DB-GPT: AI Native Data App Development framework with AWEL(Agentic Workflow Expression ", "url": "https://github.com/eosphoros-ai/DB-GPT?tab=readme-ov-file", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056282", "originalId": 220056282, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_js&utm_medium=editor_selection&utm_campaign=playground#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm3", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056285", "originalId": 220056285, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056288", "originalId": 220056288, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_js&utm_medium=editor_selection&utm_campaign=playground#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm3", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056291", "originalId": 220056291, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056294", "originalId": 220056294, "title": "(5) <PERSON> exposes the real risks and rewards of AI - YouTube", "url": "https://www.youtube.com/watch?v=b4e4TwU10YU", "favicon": "https://www.youtube.com/s/desktop/cd5cb204/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056297", "originalId": 220056297, "title": "Chatbase - Pricing", "url": "https://www.chatbase.co/pricing", "favicon": "https://www.chatbase.co/icon.ico?f061e2c391ac8892", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056300", "originalId": 220056300, "title": "Beyond ONE's AI Mandate: From Piloting to Production-Grade Intelligence - Napkin AI", "url": "https://app.napkin.ai/page/CgoiCHByb2Qtb25lEiwKBFBhZ2UaJDg5OGI4NTZmLTRkZjItNDFmMC1iYjNiLTQzNjdhNTc5ODlhMg", "favicon": "https://app.napkin.ai/favicon-16x16.png?v=2", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056303", "originalId": 220056303, "title": "Introduction | dlt Docs", "url": "https://dlthub.com/docs/intro", "favicon": "https://dlthub.com/docs/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056306", "originalId": 220056306, "title": "Google Actualités - Pour vous", "url": "https://news.google.com/foryou?hl=fr&gl=FR&ceid=FR%3Afr", "favicon": "https://lh3.googleusercontent.com/-DR60l-K8vnyi99NZovm9HlXyZwQ85GMDxiwJWzoasZYCUrPuUM_P_4Rb7ei03j-0nRs0c4F=w32", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056309", "originalId": 220056309, "title": "Introduction | dlt Docs", "url": "https://dlthub.com/docs/intro", "favicon": "https://dlthub.com/docs/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056312", "originalId": 220056312, "title": "What is Delta Lake in Databricks? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/delta#:~:text=Delta%20Lake%20is%20the%20optimized,transactions%20and%20scalable%20metadata%20handling.", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056315", "originalId": 220056315, "title": "Integrations | Customer.io", "url": "https://customer.io/integrations", "favicon": "https://customer.io/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057684", "originalId": 220057684, "title": "Meiro CDP - Composable Customer Data Platform", "url": "https://www.meiro.io/", "favicon": "https://www.meiro.io/icon.ico?09524e12951176a5", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220058038", "originalId": 220058038, "title": "Dashboard | Fivetran", "url": "https://fivetran.com/pricing-estimator", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220058044", "originalId": 220058044, "title": "Google Calendar - August 2025", "url": "https://calendar.google.com/calendar/u/0/r/month", "favicon": "https://calendar.google.com/googlecalendar/images/favicons_2020q4/calendar_15.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056327", "originalId": 220056327, "title": "<PERSON><PERSON>__<PERSON>_Couronne_June_2025.pdf - Google Drive", "url": "https://drive.google.com/file/d/1MVLGIH_geFsdYdaM60yVbE-wClysZC0u/view", "favicon": "https://ssl.gstatic.com/images/branding/product/1x/drive_2020q4_32dp.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057687", "originalId": 220057687, "title": "Presto: Free, Open-Source SQL Query Engine for any Data", "url": "https://prestodb.io/", "favicon": "https://prestodb.io/wp-content/uploads/favicon-150x150.png", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057690", "originalId": 220057690, "title": "About data platform connections | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/cloud/connect-data-platform/about-connections", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056336", "originalId": 220056336, "title": "New Tab", "url": "chrome://newtab/", "favicon": "", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220058041", "originalId": 220058041, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057693", "originalId": 220057693, "title": "Cultivating Tech Culture", "url": "https://chat.mistral.ai/chat/72c40e13-6892-4284-80fb-d65a85423662", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057696", "originalId": 220057696, "title": "Meiro CDP - Composable Customer Data Platform", "url": "https://www.meiro.io/", "favicon": "https://www.meiro.io/icon.ico?09524e12951176a5", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057699", "originalId": 220057699, "title": "Synthesia Makes AI Video Production Effortless with Generative AI on AWS | Synthesia Case Study | AW", "url": "https://aws.amazon.com/solutions/case-studies/synthesia-case-study/", "favicon": "https://a0.awsstatic.com/libra-css/images/site/fav/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057702", "originalId": 220057702, "title": "actor - Google Search", "url": "https://www.google.com/search?sa=X&sca_esv=adde10145c0e0ebd&lns_surface=44&biw=480&bih=307&hl=en-DE&cs=0&sxsrf=AE3TifPkTBFeQiUuZWaeuQMUva6-HITRmA:1754580070210&udm=24&source=lns.web.cntpubb&vsdim=480,", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220057705", "originalId": 220057705, "title": "AI and Digital Transformation Insights - <PERSON>", "url": "https://claude.ai/chat/f85849a9-2553-435f-9bc2-6d1b3ea3772c", "favicon": "https://claude.ai/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": false, "pinned": false}, {"id": "chrome-220056355", "originalId": 220056355, "title": "write a blog post for internal workvivo talking about a recent news about AI,...", "url": "https://www.perplexity.ai/search/write-a-blog-post-for-internal-cT29sUS0TaGT7335Su8UGA", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": 220056151, "active": true, "pinned": false}, {"id": "chrome-220057708", "originalId": 220057708, "title": "Section | AI workforce transformation for real ROI", "url": "https://www.sectionai.com/", "favicon": "https://cdn.prod.website-files.com/635ffd046dcb4346779f7a91/63de80b9c6567ab3cf331221_section-logo32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056363", "originalId": 220056363, "title": "The AI Strategy Summit - YouTube", "url": "https://www.youtube.com/watch?v=b96qNOEX6mg", "favicon": "https://www.youtube.com/s/desktop/33ae93e9/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056366", "originalId": 220056366, "title": "ProfAI | Section", "url": "https://prof.ai/", "favicon": "https://prof.ai/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056369", "originalId": 220056369, "title": "Post a job - Upwork", "url": "https://www.upwork.com/nx/job-post/instant/welcome", "favicon": "https://www.upwork.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056372", "originalId": 220056372, "title": "Looking For Upwork? Try Fiverr", "url": "https://www.fiverr.com/cp/fiverr-vs-upwork?utm_source=google&utm_medium=cpc&utm_campaign=g_ge-row_ln-en_dv-desktop_srv-competitors_mt-all_upwork_qg-high&utm_term=upwork_general_exact&utm_content=AdID^", "favicon": "https://fiverr-res.cloudinary.com/npm-assets/layout-service/favicon.52df53a.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057711", "originalId": 220057711, "title": "fiverr upwork alternative - Google Search", "url": "https://www.google.com/search?q=fiverr+upwork+alternative&oq=alternative+fiverr+up&gs_lcrp=EgZjaHJvbWUqCAgBEAAYFhgeMgYIABBFGDkyCAgBEAAYFhgeMg0IAhAAGIYDGIAEGIoFMg0IAxAAGIYDGIAEGIoFMg0IBBAAGIYDGIAEGIoFM", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "<PERSON><PERSON><PERSON><PERSON> Account", "url": "https://account.hubstaff.com/confirmation_sent/PgGfHajmIex+ciZHrQ7baLyM--IAkusBD5Oh3v7BTc--P3ikljwtIWa2GFDqs4mLeA==", "favicon": "https://account-assets.hubstaff.com/fadb0f08/favicon/favicon-521b5b5302434bea83e1aee91bedca029db84b4786fa33db3f973717359da2a1.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Hire Freelancers & Remote Workers For Free", "url": "https://hubstafftalent.net/", "favicon": "https://talent-assets.hubstaff.com/fd74122a/favicon-99aa5ff9ae8e860bc74fb148d35465a857f465d05b9c2e9cdc5ca0f1681a6a50.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Architecting Global Data Collaboration with Delta Sharing | Databricks Blog", "url": "https://www.databricks.com/blog/architecting-global-data-collaboration-delta-sharing", "favicon": "https://www.databricks.com/en-blog-assets/favicon-32x32.png?v=c9b9916c3b27dc51866c46b79a6e9b88", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056387", "originalId": 220056387, "title": "VAST DataEngine - Powerful Global Function Execution Engine", "url": "https://www.vastdata.com/platform/dataengine", "favicon": "https://www.vastdata.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056390", "originalId": 220056390, "title": "Statsig | The modern product development platform", "url": "https://statsig.com/", "favicon": "https://statsig.com/images/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056393", "originalId": 220056393, "title": "mcp-use Cloud", "url": "https://mcp-use.com/", "favicon": "https://mcp-use.com/favicon-black.svg", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220057717", "originalId": 220057717, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057720", "originalId": 220057720, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057723", "originalId": 220057723, "title": "HazyResearch/minions: Big & Small LLMs working together", "url": "https://github.com/hazyResearch/minions/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056806", "originalId": 220056806, "title": "Automate business processes with AI | Lleverage", "url": "https://www.lleverage.ai/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056809", "originalId": 220056809, "title": "Introducing uv: Next-Gen Python Package Manager | by <PERSON> | Medium", "url": "https://codemaker2016.medium.com/introducing-uv-next-gen-python-package-manager-b78ad39c95d7#8902", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056812", "originalId": 220056812, "title": "Motiff: AI-powered Professional Interface Design Tool", "url": "https://www.motiff.com/?ref", "favicon": "https://www.motiff.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056815", "originalId": 220056815, "title": "Genspark Agents", "url": "https://www.genspark.ai/agents?type=phone_call", "favicon": "https://www.genspark.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056818", "originalId": 220056818, "title": "Zep - AI Agent Memory", "url": "https://www.getzep.com/", "favicon": "https://cdn.prod.website-files.com/660b1c076d13b7e2967a499d/660c6dc329d8b16a8468f5ba_Asset%2017.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056821", "originalId": 220056821, "title": "Chai.new · vibe code any agent | by Langbase", "url": "https://chai.new/", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056824", "originalId": 220056824, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056827", "originalId": 220056827, "title": "Ma<PERSON>wl<PERSON>", "url": "https://www.magnowlia.com/", "favicon": "https://www.magnowlia.com/wp-content/uploads/2024/04/cropped-Magnowlia_logo_slack_blue_darksand-1-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056830", "originalId": 220056830, "title": "thomascouronne - Home", "url": "https://lightning.ai/thomascouronne/home", "favicon": "https://lightning.ai/favicon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056833", "originalId": 220056833, "title": "LoRA", "url": "https://huggingface.co/docs/diffusers/main/en/training/lora", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056836", "originalId": 220056836, "title": "ag-ui-protocol/ag-ui: AG-UI: the Agent-User Interaction Protocol. Bring Agents into Frontend Applica", "url": "https://github.com/ag-ui-protocol/ag-ui", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056839", "originalId": 220056839, "title": "Notion Mail", "url": "https://www.notion.com/product/mail", "favicon": "https://www.notion.com/front-static/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056842", "originalId": 220056842, "title": "mcp-course (Hugging Face MCP Course)", "url": "https://huggingface.co/mcp-course", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056845", "originalId": 220056845, "title": "Pythonic, Modern Workflow Orchestration For Resilient Data Platforms | Prefect", "url": "https://www.prefect.io/", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056848", "originalId": 220056848, "title": "Coding-Crashkurse/A2A-LangGraph", "url": "https://github.com/Coding-Crashkurse/A2A-LangGraph", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056851", "originalId": 220056851, "title": "ishanExtreme/a2a_mcp-example: An example showing how A2A and MCP can be used together", "url": "https://github.com/ishanExtreme/a2a_mcp-example", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056854", "originalId": 220056854, "title": "Tsadoq/a2a-mcp-tutorial: A tutorial on how to use Model Context Protocol by Anthropic and Agent2Agen", "url": "https://github.com/Tsadoq/a2a-mcp-tutorial", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056857", "originalId": 220056857, "title": "Interactive Budgeting - There is 1 AI tool For That", "url": "https://theresanaiforthat.com/interactive-budgeting/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056860", "originalId": 220056860, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/oauth/code/success?app=claude-code", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056863", "originalId": 220056863, "title": "sda814a.evoshosting.com", "url": "http://sda814a.evoshosting.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056866", "originalId": 220056866, "title": "Power BI: Pricing Plan | Microsoft Power Platform", "url": "https://www.microsoft.com/en/power-platform/products/power-bi/pricing?market=af#tabs-pill-bar-ocbbe94_tab1", "favicon": "https://www.microsoft.com/favicon.ico?v2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056869", "originalId": 220056869, "title": "Microsoft Fabric - Pricing | Microsoft Azure", "url": "https://azure.microsoft.com/en-us/pricing/details/microsoft-fabric/", "favicon": "https://azure.microsoft.com/cdn-classic/cvt-bb8a1cf1009a5b5c7760c9ad231a12f2327b0f120d671060a326ad4c7c919be9/images/icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056872", "originalId": 220056872, "title": "Outlook for macOS MCP Server by <PERSON> | PulseMCP", "url": "https://www.pulsemcp.com/servers/syedazharmbnr1-outlook-for-macos", "favicon": "https://www.pulsemcp.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056875", "originalId": 220056875, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/3d265b3f-8939-491d-a215-a45bf06c8729/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056878", "originalId": 220056878, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/2dc7e96c-c083-419d-948e-f24c7f8d9883/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056881", "originalId": 220056881, "title": "Kilo Code Documentation | Kilo Code Docs", "url": "https://kilocode.ai/docs/", "favicon": "https://kilocode.ai/docs/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056884", "originalId": 220056884, "title": "Create tables  |  Dataform  |  Google Cloud", "url": "https://cloud.google.com/dataform/docs/create-tables", "favicon": "https://www.gstatic.com/devrel-devsite/prod/v31bf0d5ece3babea9777b807f088a03e9bb2225d007f11b8410e9c896eb213a6/cloud/images/favicons/onecloud/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056887", "originalId": 220056887, "title": "Top Open Source ETL Frameworks in 2025 + Alternatives | Integrate.io", "url": "https://www.integrate.io/blog/open-source-etl-frameworks-revolutionizing-data-integration/", "favicon": "https://www.integrate.io/blog/assets/favicon-7b3fac9c5056e956d9a9c853a88d9979229875e342663df97bd54b2eecee2827.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056890", "originalId": 220056890, "title": "Dremio Apache Iceberg Solutions and Capabilities", "url": "https://www.dremio.com/platform/apache-iceberg/", "favicon": "https://www.dremio.com//wp-content/themes/subsurface/assets/_corp/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056893", "originalId": 220056893, "title": "Domain", "url": "https://ap.www.namecheap.com/domains/domaincontrolpanel/ecoptimum.tech/domain", "favicon": "https://www.namecheap.com/assets/img/nc-icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056896", "originalId": 220056896, "title": "Customer Stories | Hightouch", "url": "https://hightouch.com/customers", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056899", "originalId": 220056899, "title": "Perplexity", "url": "https://www.perplexity.ai/?login-source=oneTapHome&login-new=false", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056902", "originalId": 220056902, "title": "Integrations | Hightouch", "url": "https://hightouch.com/integrations?type=destination", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056905", "originalId": 220056905, "title": "About MetricFlow | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/build/about-metricflow", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056908", "originalId": 220056908, "title": "Braze User Guide", "url": "https://www.braze.com/docs/user_guide/introduction", "favicon": "https://www.braze.com/docs/assets/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056911", "originalId": 220056911, "title": "Base44", "url": "https://app.base44.com/", "favicon": "https://app.base44.com/logo_v3.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056914", "originalId": 220056914, "title": "Plum 101: Your Questions Answered", "url": "https://blog.withplum.com/plum-user-questions-answered/", "favicon": "https://blog.withplum.com/content/images/size/w256h256/2019/07/img_plum_avatar.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056917", "originalId": 220056917, "title": "Dittofeed | Open-source Customer Engagement for Automated Messaging", "url": "https://www.dittofeed.com/", "favicon": "https://cdn.prod.website-files.com/664393f5bd2b0c6d4fdf5437/6660af223efe69a76107dc35_Dittofeed_newLogo_graphicOnly_favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056920", "originalId": 220056920, "title": "(1) New Messages!", "url": "https://www.beyondtrust.com/brand?utm_source=google&utm_medium=cpc&utm_campaign=9336390457&utm_content=************&utm_term=beyond%20trust&gad_source=1&gad_campaignid=9336390457&gbraid=0AAAAACjgS9pCF", "favicon": "https://www.beyondtrust.com/favicon.png?v=5", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056923", "originalId": 220056923, "title": "Microsoft Teams Meets Databricks Genie API: A Complete Setup Guide | by <PERSON> | Medium", "url": "https://medium.com/@ryan-bates/microsoft-teams-meets-databricks-genie-api-a-complete-setup-guide-81f629ace634", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056926", "originalId": 220056926, "title": "Predictive Analytics Software For Humans | Pecan AI", "url": "https://www.pecan.ai/home-2-1/", "favicon": "https://www.pecan.ai/wp-content/uploads/2023/11/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Claude <PERSON> overview - Anthropic", "url": "https://docs.anthropic.com/en/docs/claude-code/overview", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/anthropic/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/login?selectAccount=true&returnTo=%2Foauth%2Fauthorize%3Fcode%3Dtrue%26client_id%3D9d1c250a-e61b-44d9-88ed-5944d1962f5e%26response_type%3Dcode%26redirect_uri%3Dhttp%253A%", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Aider-AI/aider: aider is AI pair programming in your terminal", "url": "https://github.com/Aider-AI/aider", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "LLM Guardrail for Employees | Secure AI Workflow | WitnessAI", "url": "https://witness.ai/for-employees/", "favicon": "https://witness.ai/favicon.ico?v=2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ideas - Google Sheets", "url": "https://docs.google.com/spreadsheets/d/1JO2j6-oeRez-w-NUyYAYXzIP_gbJrX5ZwiM_50WE8-U/edit?gid=0#gid=0", "favicon": "https://ssl.gstatic.com/docs/spreadsheets/spreadsheets_2023q4.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056944", "originalId": 220056944, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/?r=0", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056947", "originalId": 220056947, "title": "what is the valuation of", "url": "https://www.perplexity.ai/search/what-is-the-valuation-of-7A3oVzgBQ9qbh4ua_vy80g", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056950", "originalId": 220056950, "title": "what are the critical financial numbers for www.zand.ae/", "url": "https://www.perplexity.ai/search/what-are-the-critical-financia-PqF_27O.Sp6T3eJoUarbfQ", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056953", "originalId": 220056953, "title": "OpenInterpreter/open-interpreter: A natural language interface for computers", "url": "https://github.com/OpenInterpreter/open-interpreter", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056956", "originalId": 220056956, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056959", "originalId": 220056959, "title": "AI APIs for I - TAAFT®", "url": "https://theresanaiforthat.com/apis/s/i/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056962", "originalId": 220056962, "title": "[FREEMOBILE] **********: Nouveau message de: +15855950645 - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220056965", "originalId": 220056965, "title": "Augment Code", "url": "https://login.augmentcode.com/u/login/identifier?state=hKFo2SBhQzVnVFdpdXVjQkdpWHh0UTNDREFnbVlRdFR5MGRNU6Fur3VuaXZlcnNhbC1sb2dpbqN0aWTZIDBOYnc5SnhaUWwybm5rV1MyZzZ5WG50enFoQ1dmWkpDo2NpZNkgd2xMVFZXR0RmS", "favicon": "https://www.augmentcode.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056968", "originalId": 220056968, "title": "MCP Servers", "url": "https://mcp.so/", "favicon": "https://mcp.so/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056971", "originalId": 220056971, "title": "Separate1", "url": "https://cdn.prod.website-files.com/6537bd39ea748ca6a0e62198/67f8209f32d9a9a52fdf5971_Brochure.pdf", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056974", "originalId": 220056974, "title": "Opera Neon | Agentic AI browser | Opera", "url": "https://www.operaneon.com/", "favicon": "https://www.operaneon.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056977", "originalId": 220056977, "title": "Kohonen Map | Food4Rhino", "url": "https://www.food4rhino.com/en/app/kohonen-map", "favicon": "https://www.food4rhino.com/sites/default/files/public/f4r/images/icons/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056980", "originalId": 220056980, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VGFr2zAQ_Sui0LLBQtnWfcmHQZqkJVtMQp2uA3UUxVESUUcKltw1jP333Z0kR67bfQjRvfd0kk93789JYVbypH-yqcR-yxaje83Y6SmbioOs2Mc-G9bWmR2sJ9rJShROGc1yU1eFtKi19TJsNXWx3RulHeGML", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056983", "originalId": 220056983, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056986", "originalId": 220056986, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_live_editor&utm_medium=toggle#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056989", "originalId": 220056989, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNqrVkrOT0lVslJSqgUAFW4DVg", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056992", "originalId": 220056992, "title": "OneOptimizer - AI-Powered Budgeting", "url": "http://localhost:8503/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056995", "originalId": 220056995, "title": "Meeting Summaries, Transcripts, AI Notetaker & Enterprise Search | read.ai", "url": "https://www.read.ai/", "favicon": "https://cdn.prod.website-files.com/614e5e239ea0f25fe5b6a797/615f7213c6091b296dd6ebd0_ReadLogomark_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220056998", "originalId": 220056998, "title": "Quickstart (with cloud resources) | Weaviate Documentation", "url": "https://docs.weaviate.io/weaviate/quickstart", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057001", "originalId": 220057001, "title": "How to install Weaviate | Weaviate Documentation", "url": "https://docs.weaviate.io/deploy/installation-guides", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057004", "originalId": 220057004, "title": "Build a Weekly AI Trend Alerter with arXiv and Weaviate | n8n workflow template", "url": "https://n8n.io/workflows/5817-build-a-weekly-ai-trend-alerter-with-arxiv-and-weaviate/", "favicon": "https://n8n.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057007", "originalId": 220057007, "title": "Quickstart (with cloud resources) | Weaviate Documentation", "url": "https://docs.weaviate.io/weaviate/quickstart", "favicon": "https://docs.weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057010", "originalId": 220057010, "title": "The AI-native database developers love | Weaviate", "url": "https://weaviate.io/", "favicon": "https://weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057013", "originalId": 220057013, "title": "The Agentic Platform for Personalization | Bloomreach", "url": "https://www.bloomreach.com/en", "favicon": "https://www.bloomreach.com/wp-content/uploads/2024/04/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058065", "originalId": 220058065, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057021", "originalId": 220057021, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057024", "originalId": 220057024, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057027", "originalId": 220057027, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057030", "originalId": 220057030, "title": "OSS Specifications", "url": "https://www.mage.ai/oss", "favicon": "https://framerusercontent.com/images/ofrGuj4GtrMewS5s195d0pFIkGY.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057033", "originalId": 220057033, "title": "apache/beam: Apache Beam is a unified programming model for Batch and Streaming data processing.", "url": "https://github.com/apache/beam", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057036", "originalId": 220057036, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057039", "originalId": 220057039, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057042", "originalId": 220057042, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057045", "originalId": 220057045, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057048", "originalId": 220057048, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057051", "originalId": 220057051, "title": "clerk.copilotkit.ai", "url": "https://clerk.copilotkit.ai/v1/client/handshake?redirect_url=https%3A%2F%2Fcloud.copilotkit.ai%2Fonboarding", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057054", "originalId": 220057054, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057057", "originalId": 220057057, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057060", "originalId": 220057060, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057063", "originalId": 220057063, "title": "Getting Started: Deploying | Next.js", "url": "https://nextjs.org/docs/app/getting-started/deploying", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057066", "originalId": 220057066, "title": "supabase-orange-river | tom's projects | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/sql/9c078490-4c2a-48c9-945c-48816bec561a", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057069", "originalId": 220057069, "title": "glossary | Railway", "url": "https://railway.com/project/************************************/service/1fa739df-d725-4abb-9068-74c083b27998/settings?environmentId=23f319c3-14e1-469b-a35c-46a809dda1d0#networking-public", "favicon": "https://railway.com/favicons/favicon-light.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057072", "originalId": 220057072, "title": "Corporate Glossary", "url": "https://glossary-production.up.railway.app/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057075", "originalId": 220057075, "title": "alt144/glossary at dev-initial", "url": "https://github.com/alt144/glossary/tree/dev-initial", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057078", "originalId": 220057078, "title": "Augment Code Billing", "url": "https://billing.augmentcode.com/p/session/live_YWNjdF8xUXB6TUFBbXY5Z1Y5NnR1LF9TRFlLWVVMNzdVWGxHMjh0TzczVXVzc2lPdDVZc3lT0100qddA0uZq", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Blockchain Auditor", "url": "http://localhost:8501/", "favicon": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>%F0%9F%93%8A</text></svg>", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment Signup - Create a free account | Twilio Segment", "url": "https://segment.com/signup/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fverify%3Fresponse%3DeyJ1c2VyIjp7ImlkIjoid0Y0NmJOblF2YVFxd01ZTk5GdGZBUiIsIm5hbWUiOiJUaG9tYXMgQ291cm9ubsOpIiwiZW1haWwiOiJ0aG9tYXMuY291cm9ubmVAYmV5b25kLm9uZSIsIm", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "clevertap - Google Search", "url": "https://www.google.com/search?q=clevertap&oq=clevertap&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBCDEzNzJqMGoxqAIAsAIA&sourceid=chrome&ie=UTF-8", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058068", "originalId": 220058068, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057096", "originalId": 220057096, "title": "diffbot/Llama-3.3-Diffbot-Small-XL-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.3-Diffbot-Small-XL-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057099", "originalId": 220057099, "title": "diffbot/Llama-3.1-Diffbot-Small-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.1-Diffbot-Small-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057102", "originalId": 220057102, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fbeyond-thomas-couronne%2Fintegration-health", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057105", "originalId": 220057105, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057108", "originalId": 220057108, "title": "doc-rag-support-agent-1385 - alt144", "url": "https://chai.new/alt144/doc-rag-support-agent-1385?v=1", "favicon": "https://command.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057111", "originalId": 220057111, "title": "API Keys | Exa API", "url": "https://dashboard.exa.ai/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057114", "originalId": 220057114, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057117", "originalId": 220057117, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057120", "originalId": 220057120, "title": "SQLite Home Page", "url": "https://sqlite.org/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057123", "originalId": 220057123, "title": "alt144/glossary at dev", "url": "https://github.com/alt144/glossary/tree/dev", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057126", "originalId": 220057126, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057129", "originalId": 220057129, "title": "API | Supabase", "url": "https://supabase.com/dashboard/project/wyegfyhilfcjawhyekmp/api", "favicon": "https://supabase.com/dashboard/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057132", "originalId": 220057132, "title": "Local Development & CLI | Supabase Docs", "url": "https://supabase.com/docs/guides/local-development", "favicon": "https://supabase.com/docs/favicon/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057135", "originalId": 220057135, "title": "(3) Regardez France Inter - YouTube", "url": "https://www.youtube.com/watch?v=ACPnVZDNjJs", "favicon": "https://www.youtube.com/s/desktop/2253fa3d/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057138", "originalId": 220057138, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057141", "originalId": 220057141, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057144", "originalId": 220057144, "title": "Page 1 | Untitled | Editor | Retool", "url": "https://corona.retool.com/editor/baa74f3e-5267-11f0-add6-337947c70add/Untitled/page1", "favicon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8yXzI2KSI+CjxtYXNrIGlkPSJtYXNrMF8yXzI2IiBzdHlsZT0ibWFzay10eXBlOmx1bWluYW5jZSIgbWFza1VuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeD0iMCIgeT0iMCIgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiPgo8cGF0aCBkPSJNMTI4IDBIMFYxMjhIMTI4VjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L21hc2s+CjxnIG1hc2s9InVybCgjbWFzazBfMl8yNikiPgo8cGF0aCBkPSJNODAuNjQxNiAxNS42NjY3QzgwLjY0MTYgMTIuOTA1MiA3OC40MDMgMTAuNjY2NiA3NS42NDE2IDEwLjY2NjZIMTguOTc0QzE2LjIxMjYgMTAuNjY2NiAxMy45NzQgMTIuOTA1MiAxMy45NzQgMTUuNjY2N1YzOS4wMDA0QzEzLjk3NCA0MS43NjE4IDE2LjIxMjYgNDQuMDAwNCAxOC45NzQgNDQuMDAwNEg3NS42NDE2Qzc4LjQwMyA0NC4wMDA0IDgwLjY0MTYgNDYuMjM5IDgwLjY0MTYgNDkuMDAwNVY1NS42NjczQzgwLjY0MTYgNTguNDI4NyA3OC40MDMgNjAuNjY3NCA3NS42NDE2IDYwLjY2NzRIMzguOTc0M0MzNi4yMTI5IDYwLjY2NzQgMzMuOTc0MyA2Mi45MDYgMzMuOTc0MyA2NS42Njc0Vjg5LjAwMTFDMzMuOTc0MyA5MS43NjI2IDM2LjIxMjkgOTQuMDAxMiAzOC45NzQzIDk0LjAwMTJINzUuNjQxNkM3OC40MDMgOTQuMDAxMiA4MC42NDE2IDk2LjIzOTggODAuNjQxNiA5OS4wMDEzVjExMi4zMzVDODAuNjQxNiAxMTUuMDk2IDgyLjg4MDMgMTE3LjMzNSA4NS42NDE3IDExNy4zMzVIMTA4Ljk3NUMxMTEuNzM3IDExNy4zMzUgMTEzLjk3NSAxMTUuMDk2IDExMy45NzUgMTEyLjMzNVY4OS4wMDExQzExMy45NzUgODYuMjM5NyAxMTEuNzM3IDg0LjAwMSAxMDguOTc1IDg0LjAwMUg4NS42NDE3QzgyLjg4MDMgODQuMDAxIDgwLjY0MTYgODEuNzYyNCA4MC42NDE2IDc5LjAwMVY3Mi4zMzQyQzgwLjY0MTYgNjkuNTcyNyA4Mi44ODAzIDY3LjMzNDEgODUuNjQxNyA2Ny4zMzQxSDEwOC45NzVDMTExLjczNyA2Ny4zMzQxIDExMy45NzUgNjUuMDk1NSAxMTMuOTc1IDYyLjMzNDFWMzkuMDAwNEMxMTMuOTc1IDM2LjIzODkgMTExLjczNyAzNC4wMDAzIDEwOC45NzUgMzQuMDAwM0g4NS42NDE3QzgyLjg4MDMgMzQuMDAwMyA4MC42NDE2IDMxLjc2MTcgODAuNjQxNiAyOS4wMDAyVjE1LjY2NjdaIiBmaWxsPSIjOTk5OTk5Ii8+CjwvZz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8yXzI2Ij4KPHJlY3Qgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057147", "originalId": 220057147, "title": "Watch Best New Movies & Shows from Biggest Studios | OSN+", "url": "https://osnplus.com/en-ae?gad_source=1&gad_campaignid=***********&gbraid=0AAAAABnYLV5z0fQSHtIr0bHl-FtL4oe7M&gclid=CjwKCAjw3_PCBhA2EiwAkH_j4hMF4mhWhPnYZGYFq95esj3UQmD1aDjzi1WP1Y5sxKodfouisetZoRoCnYAQAv", "favicon": "https://osnplus.com/favicon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057150", "originalId": 220057150, "title": "Fabric – your self-organizing workspace and file explorer", "url": "https://fabric.so/welcome", "favicon": "https://framerusercontent.com/images/I44tR0tcOIpWRJxbR6x608smGE.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057153", "originalId": 220057153, "title": "Introducing the Weaviate Query Agent | Weaviate", "url": "https://weaviate.io/blog/query-agent?utm_source=channels&utm_medium=vs_social&utm_campaign=agents&utm_content=honeypot_post_680309399", "favicon": "https://weaviate.io/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057156", "originalId": 220057156, "title": "Menlo Research", "url": "https://menlo.ai/", "favicon": "https://menlo.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057159", "originalId": 220057159, "title": "3Blue1Brown - But what is a GPT? Visual intro to Transformers | Deep learning, chapter 5", "url": "https://www.3blue1brown.com/lessons/gpt", "favicon": "https://www.3blue1brown.com/favicons/favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057162", "originalId": 220057162, "title": "Why MCP Won - Latent.Space", "url": "https://www.latent.space/p/why-mcp-won", "favicon": "https://substackcdn.com/image/fetch/$s_!J-V9!,f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Ffeff84b7-b3cb-4c54-95d5-bd2a208f3f6d%2Ffavicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057165", "originalId": 220057165, "title": "Amazon Web Services (AWS)", "url": "https://profile.aws.amazon.com/#/profile/details", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057168", "originalId": 220057168, "title": "(87) Inbox | <EMAIL> | Proton Mail", "url": "https://mail.proton.me/u/4/inbox", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=b6cgradae1v", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057171", "originalId": 220057171, "title": "<PERSON> | The AI Software Engineer", "url": "https://devin.ai/", "favicon": "https://devin.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220058086", "originalId": 220058086, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057179", "originalId": 220057179, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057182", "originalId": 220057182, "title": "alt144/glossary", "url": "https://github.com/alt144/glossary", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057185", "originalId": 220057185, "title": "HazyResearch/minions: Big & Small LLMs working together", "url": "https://github.com/hazyResearch/minions/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057188", "originalId": 220057188, "title": "Automate business processes with AI | Lleverage", "url": "https://www.lleverage.ai/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057191", "originalId": 220057191, "title": "Introducing uv: Next-Gen Python Package Manager | by <PERSON> | Medium", "url": "https://codemaker2016.medium.com/introducing-uv-next-gen-python-package-manager-b78ad39c95d7#8902", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057194", "originalId": 220057194, "title": "Motiff: AI-powered Professional Interface Design Tool", "url": "https://www.motiff.com/?ref", "favicon": "https://www.motiff.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057197", "originalId": 220057197, "title": "Genspark Agents", "url": "https://www.genspark.ai/agents?type=phone_call", "favicon": "https://www.genspark.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057200", "originalId": 220057200, "title": "Zep - AI Agent Memory", "url": "https://www.getzep.com/", "favicon": "https://cdn.prod.website-files.com/660b1c076d13b7e2967a499d/660c6dc329d8b16a8468f5ba_Asset%2017.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057203", "originalId": 220057203, "title": "Chai.new · vibe code any agent | by Langbase", "url": "https://chai.new/", "favicon": "https://chai.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057206", "originalId": 220057206, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057209", "originalId": 220057209, "title": "Ma<PERSON>wl<PERSON>", "url": "https://www.magnowlia.com/", "favicon": "https://www.magnowlia.com/wp-content/uploads/2024/04/cropped-Magnowlia_logo_slack_blue_darksand-1-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057212", "originalId": 220057212, "title": "thomascouronne - Home", "url": "https://lightning.ai/thomascouronne/home", "favicon": "https://lightning.ai/favicon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057215", "originalId": 220057215, "title": "LoRA", "url": "https://huggingface.co/docs/diffusers/main/en/training/lora", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057218", "originalId": 220057218, "title": "HEIMOANA Current position (Unknown, MMSI *********) - VesselFinder", "url": "https://www.vesselfinder.com/?mmsi=*********", "favicon": "https://www.vesselfinder.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057221", "originalId": 220057221, "title": "UAE Public Holidays 2025 - <PERSON>ly Ann<PERSON>", "url": "https://www.bayzat.com/blog/public-holidays-uae/#:~:text=Eid%2Dal%2DAdha%20%E2%80%93%206%2D8%2C%20June%2C%202025&text=The%20UAE%20Moon%20Sighting%20Committee,will%20extend%20till%20June%208.", "favicon": "https://www.bayzat.com/blog/wp-content/uploads/2022/05/cropped-icon-48x48-1.webp", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Dubai to Berlin | Google Flights", "url": "https://www.google.com/travel/flights/search?tfs=CBwQAhopEgoyMDI1LTA2LTA1ag0IAhIJL20vMDFmMDhycgwIAxIIL20vMDE1NnEaKRIKMjAyNS0wNi0wOWoMCAMSCC9tLzAxNTZxcg0IAhIJL20vMDFmMDhyQAFIAXABggELCP___________wGYAQE", "favicon": "https://www.gstatic.com/images/branding/product/2x/travel_flights_48dp.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ag-ui-protocol/ag-ui: AG-UI: the Agent-User Interaction Protocol. Bring Agents into Frontend Applica", "url": "https://github.com/ag-ui-protocol/ag-ui", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Notion Mail", "url": "https://www.notion.com/product/mail", "favicon": "https://www.notion.com/front-static/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "mcp-course (Hugging Face MCP Course)", "url": "https://huggingface.co/mcp-course", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Proton Mail: Sign-in", "url": "https://account.proton.me/mail", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=6iavtfjbw24", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Proton Mail: Sign-in", "url": "https://account.proton.me/mail", "favicon": "https://mail.proton.me/assets/static/favicon.d47d3d0bef6d338e377a.svg?v=6iavtfjbw24", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Oracle Cloud Free Tier Signup", "url": "https://signup.cloud.oracle.com/?verify_email=eyJhbGciOiJIUzI1NiJ9.eyJjYXB0Y2hhSnd0VG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlKOS5leUp6ZFdJaU9pSnZZMmt0YzJsbmJuVndJaXdpYVhOeklqb2lhSFIwY0hNNkx5OXphV2R1ZFhBdWIzS", "favicon": "https://signup.cloud.oracle.com/oraclefavicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Oracle Cloud Free Tier Signup", "url": "https://signup.cloud.oracle.com/?verify_email=eyJhbGciOiJIUzI1NiJ9.eyJjYXB0Y2hhSnd0VG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlKOS5leUp6ZFdJaU9pSnZZMmt0YzJsbmJuVndJaXdpYVhOeklqb2lhSFIwY0hNNkx5OXphV2R1ZFhBdWIzS", "favicon": "https://signup.cloud.oracle.com/oraclefavicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057251", "originalId": 220057251, "title": "What is the Databricks CLI? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/dev-tools/cli", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057254", "originalId": 220057254, "title": "New Tab", "url": "chrome://new-tab-page/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057255", "originalId": 220057255, "title": "GCPing.com", "url": "https://gcping.com/", "favicon": "https://gcping.com/icon.662224a4.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057258", "originalId": 220057258, "title": "cloudping.info", "url": "https://www.cloudping.info/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057261", "originalId": 220057261, "title": "Pricing | Prefect Cloud", "url": "https://www.prefect.io/pricing", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057264", "originalId": 220057264, "title": "Coding-Crashkurse/A2A-LangGraph", "url": "https://github.com/Coding-Crashkurse/A2A-LangGraph", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057267", "originalId": 220057267, "title": "ishanExtreme/a2a_mcp-example: An example showing how A2A and MCP can be used together", "url": "https://github.com/ishanExtreme/a2a_mcp-example", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057270", "originalId": 220057270, "title": "Tsadoq/a2a-mcp-tutorial: A tutorial on how to use Model Context Protocol by Anthropic and Agent2Agen", "url": "https://github.com/Tsadoq/a2a-mcp-tutorial", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057273", "originalId": 220057273, "title": "Interactive Budgeting - There is 1 AI tool For That", "url": "https://theresanaiforthat.com/interactive-budgeting/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057276", "originalId": 220057276, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/oauth/code/success?app=claude-code", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057279", "originalId": 220057279, "title": "sda814a.evoshosting.com", "url": "http://sda814a.evoshosting.com/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057282", "originalId": 220057282, "title": "Power BI: Pricing Plan | Microsoft Power Platform", "url": "https://www.microsoft.com/en/power-platform/products/power-bi/pricing?market=af#tabs-pill-bar-ocbbe94_tab1", "favicon": "https://www.microsoft.com/favicon.ico?v2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057285", "originalId": 220057285, "title": "Microsoft Fabric - Pricing | Microsoft Azure", "url": "https://azure.microsoft.com/en-us/pricing/details/microsoft-fabric/", "favicon": "https://azure.microsoft.com/cdn-classic/cvt-bb8a1cf1009a5b5c7760c9ad231a12f2327b0f120d671060a326ad4c7c919be9/images/icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057288", "originalId": 220057288, "title": "Outlook for macOS MCP Server by <PERSON> | PulseMCP", "url": "https://www.pulsemcp.com/servers/syedazharmbnr1-outlook-for-macos", "favicon": "https://www.pulsemcp.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057291", "originalId": 220057291, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/3d265b3f-8939-491d-a215-a45bf06c8729/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057294", "originalId": 220057294, "title": "Editor | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/2dc7e96c-c083-419d-948e-f24c7f8d9883/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057297", "originalId": 220057297, "title": "Kilo Code Documentation | Kilo Code Docs", "url": "https://kilocode.ai/docs/", "favicon": "https://kilocode.ai/docs/img/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057300", "originalId": 220057300, "title": "Create tables  |  Dataform  |  Google Cloud", "url": "https://cloud.google.com/dataform/docs/create-tables", "favicon": "https://www.gstatic.com/devrel-devsite/prod/v31bf0d5ece3babea9777b807f088a03e9bb2225d007f11b8410e9c896eb213a6/cloud/images/favicons/onecloud/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057303", "originalId": 220057303, "title": "Top Open Source ETL Frameworks in 2025 + Alternatives | Integrate.io", "url": "https://www.integrate.io/blog/open-source-etl-frameworks-revolutionizing-data-integration/", "favicon": "https://www.integrate.io/blog/assets/favicon-7b3fac9c5056e956d9a9c853a88d9979229875e342663df97bd54b2eecee2827.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057306", "originalId": 220057306, "title": "Dremio Apache Iceberg Solutions and Capabilities", "url": "https://www.dremio.com/platform/apache-iceberg/", "favicon": "https://www.dremio.com//wp-content/themes/subsurface/assets/_corp/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057309", "originalId": 220057309, "title": "Domain", "url": "https://ap.www.namecheap.com/domains/domaincontrolpanel/ecoptimum.tech/domain", "favicon": "https://www.namecheap.com/assets/img/nc-icon/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057312", "originalId": 220057312, "title": "Customer Stories | Hightouch", "url": "https://hightouch.com/customers", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057315", "originalId": 220057315, "title": "Perplexity", "url": "https://www.perplexity.ai/?login-source=oneTapHome&login-new=false", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057732", "originalId": 220057732, "title": "Qualtrics S&R - Google Search", "url": "https://www.google.com/search?q=Qualtrics+S%26R&oq=Qualtrics+S%26R&gs_lcrp=EgZjaHJvbWUyBggAEEUYOdIBBzM0M2owajGoAgCwAgA&sourceid=chrome&ie=UTF-8", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057321", "originalId": 220057321, "title": "Integrations | Hightouch", "url": "https://hightouch.com/integrations?type=destination", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057324", "originalId": 220057324, "title": "About MetricFlow | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/build/about-metricflow", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057327", "originalId": 220057327, "title": "Braze User Guide", "url": "https://www.braze.com/docs/user_guide/introduction", "favicon": "https://www.braze.com/docs/assets/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057330", "originalId": 220057330, "title": "Base44", "url": "https://app.base44.com/", "favicon": "https://app.base44.com/logo_v3.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057735", "originalId": 220057735, "title": "plum financial assistant - Google Search", "url": "https://www.google.com/search?q=plum+financial+assistant&sca_esv=eee3cc9543ede721&sxsrf=AE3TifM0RiKIxIPidc-tRkxYjpYS68_uMw%3A1750414507130&ei=qzRVaN_NB8mKi-gP9eC9yAQ&ved=0ahUKEwifz6CX4v-NAxVJxQIHHXVwD", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057336", "originalId": 220057336, "title": "Dittofeed | Open-source Customer Engagement for Automated Messaging", "url": "https://www.dittofeed.com/", "favicon": "https://cdn.prod.website-files.com/664393f5bd2b0c6d4fdf5437/6660af223efe69a76107dc35_Dittofeed_newLogo_graphicOnly_favicon.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057339", "originalId": 220057339, "title": "(1) New Messages!", "url": "https://www.beyondtrust.com/brand?utm_source=google&utm_medium=cpc&utm_campaign=9336390457&utm_content=************&utm_term=beyond%20trust&gad_source=1&gad_campaignid=9336390457&gbraid=0AAAAACjgS9pCF", "favicon": "https://www.beyondtrust.com/favicon.png?v=5", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057342", "originalId": 220057342, "title": "Microsoft Teams Meets Databricks Genie API: A Complete Setup Guide | by <PERSON> | Medium", "url": "https://medium.com/@ryan-bates/microsoft-teams-meets-databricks-genie-api-a-complete-setup-guide-81f629ace634", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057345", "originalId": 220057345, "title": "Predictive Analytics Software For Humans | Pecan AI", "url": "https://www.pecan.ai/home-2-1/", "favicon": "https://www.pecan.ai/wp-content/uploads/2023/11/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Claude <PERSON> overview - Anthropic", "url": "https://docs.anthropic.com/en/docs/claude-code/overview", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/anthropic/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/login?selectAccount=true&returnTo=%2Foauth%2Fauthorize%3Fcode%3Dtrue%26client_id%3D9d1c250a-e61b-44d9-88ed-5944d1962f5e%26response_type%3Dcode%26redirect_uri%3Dhttp%253A%", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Aider-AI/aider: aider is AI pair programming in your terminal", "url": "https://github.com/Aider-AI/aider", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "LLM Guardrail for Employees | Secure AI Workflow | WitnessAI", "url": "https://witness.ai/for-employees/", "favicon": "https://witness.ai/favicon.ico?v=2", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ideas - Google Sheets", "url": "https://docs.google.com/spreadsheets/d/1JO2j6-oeRez-w-NUyYAYXzIP_gbJrX5ZwiM_50WE8-U/edit?gid=0#gid=0", "favicon": "https://ssl.gstatic.com/docs/spreadsheets/spreadsheets_2023q4.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057363", "originalId": 220057363, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/?r=0", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057366", "originalId": 220057366, "title": "what is the valuation of", "url": "https://www.perplexity.ai/search/what-is-the-valuation-of-7A3oVzgBQ9qbh4ua_vy80g", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057369", "originalId": 220057369, "title": "what are the critical financial numbers for www.zand.ae/", "url": "https://www.perplexity.ai/search/what-are-the-critical-financia-PqF_27O.Sp6T3eJoUarbfQ", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057372", "originalId": 220057372, "title": "OpenInterpreter/open-interpreter: A natural language interface for computers", "url": "https://github.com/OpenInterpreter/open-interpreter", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057375", "originalId": 220057375, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057378", "originalId": 220057378, "title": "AI APIs for I - TAAFT®", "url": "https://theresanaiforthat.com/apis/s/i/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058074", "originalId": 220058074, "title": "Inbox (73) - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057384", "originalId": 220057384, "title": "XLSTAT | Statistical Software for Excel", "url": "https://www.xlstat.com/", "favicon": "https://cdn.xlstat.com/dist/assets/img/favicon.svg?v=af378", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057387", "originalId": 220057387, "title": "ChatLLM Teams", "url": "https://apps.abacus.ai/chatllm/?appId=73da811f2", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057390", "originalId": 220057390, "title": "ChatLLM - AI Chat And Agents on the App Store", "url": "https://apps.apple.com/us/app/chatllm-ai-chat-and-agents/id6502844681", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057393", "originalId": 220057393, "title": "Abacus.AI - Project - test_1 - Datasets", "url": "https://abacus.ai/app/dataset_list/1503e157a4", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "New Tab", "url": "chrome://new-tab-page/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Abacus.AI - CodeLLM", "url": "https://codellm.abacus.ai/", "favicon": "https://codellm.abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "app.augmentcode.com/account/subscription", "url": "https://app.augmentcode.com/account/subscription", "favicon": "https://app.augmentcode.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "ChatLLM Teams", "url": "https://apps.abacus.ai/chatllm/?appId=appllm_engineer", "favicon": "https://abacus.ai/static/imgs/hp_chat_llm.webp", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Abacus.AI - MCP Servers How-To", "url": "https://abacus.ai/help/howTo/chatllm/mcp_servers_how_to", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "modelcontextprotocol/servers: Model Context Protocol Servers", "url": "https://github.com/modelcontextprotocol/servers", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057412", "originalId": 220057412, "title": "MCP Servers", "url": "https://mcp.so/", "favicon": "https://mcp.so/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057415", "originalId": 220057415, "title": "Abacus.AI - Project - test_1 - Dashboard", "url": "https://abacus.ai/app/projects/1503e157a4?doUpload=true", "favicon": "https://abacus.ai/static/icon2/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057418", "originalId": 220057418, "title": "Tab Manager - sort, group and more - Chrome Web Store", "url": "https://chromewebstore.google.com/detail/tab-manager-sort-group-an/hjobkgjcapaiplinfaeipeefmjcdepml?hl=en", "favicon": "https://ssl.gstatic.com/chrome/webstore/images/icon_48px.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057421", "originalId": 220057421, "title": "Extensions", "url": "chrome://extensions/", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057422", "originalId": 220057422, "title": "Settings - Appearance", "url": "chrome://settings/appearance", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220057630", "originalId": 220057630, "title": "HVR 6 Documentation | Fivetran Documentation", "url": "https://fivetran.com/docs/hvr6", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220058077", "originalId": 220058077, "title": "How I use LLMs - YouTube", "url": "https://www.youtube.com/watch?v=EWvNQjAaOHw", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057428", "originalId": 220057428, "title": "FinGPT: The Future of Financial Analysis — Revolutionizing Markets with Open-Source AI | by JIN | 𝐀", "url": "https://medium.com/aimonks/fingpt-the-future-of-financial-analysis-revolutionizing-markets-with-open-source-ai-94684def1889", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057431", "originalId": 220057431, "title": "Borderless AI Unveils World's First HR AI Agent, <PERSON><PERSON><PERSON>, Powered by Cohere's RAG System", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057434", "originalId": 220057434, "title": "lehoanglong95/rag-all-in-one: 🧠 Guide to Building RAG (Retrieval-Augmented Generation) Applications", "url": "https://github.com/lehoanglong95/rag-all-in-one", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057437", "originalId": 220057437, "title": "Agentic AI: Single vs Multi-Agent Systems | Towards Data Science", "url": "https://towardsdatascience.com/agentic-ai-single-vs-multi-agent-systems/", "favicon": "https://towardsdatascience.com/wp-content/uploads/2025/02/cropped-Favicon-32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057440", "originalId": 220057440, "title": "Le Chat - Mistral AI", "url": "https://chat.mistral.ai/chat/86f10712-d368-4d55-9a55-226c446b81da", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057443", "originalId": 220057443, "title": "Shubhamsaboo/awesome-llm-apps: Collection of awesome LLM apps with AI Agents and RAG using OpenAI, A", "url": "https://github.com/Shubhamsaboo/awesome-llm-apps/tree/main", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057446", "originalId": 220057446, "title": "openai/codex: Lightweight coding agent that runs in your terminal", "url": "https://github.com/openai/codex", "favicon": "https://github.githubassets.com/favicons/favicon-dark.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057449", "originalId": 220057449, "title": "Agents - Agent Development Kit", "url": "https://google.github.io/adk-docs/agents/", "favicon": "https://google.github.io/adk-docs/assets/agent-development-kit.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057452", "originalId": 220057452, "title": "cloud.copilotkit.ai", "url": "https://cloud.copilotkit.ai/onboarding", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057455", "originalId": 220057455, "title": "Introduction", "url": "https://docs.copilotkit.ai/crewai-flows", "favicon": "https://docs.copilotkit.ai/icon.png?3e7bfc214476e76a", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057458", "originalId": 220057458, "title": "Online FlowChart & Diagrams Editor - Mermaid Live Editor", "url": "https://mermaid.live/edit#pako:eNq1V2tv2zYU_SuECvTL4sai5Ee0oYAt2Ykxu4kfK9DNw0BJV45gifIoqolb9L-PFCnbitW5HboEMHgueQ_PfZC0PxtBFoLhGK1Wa02DjEbxxllThBKyzwruoCh-hnBNy-koyZ6CR8I4mi7EmrzwN4zsHtGt6_41Wk3_WBtih", "favicon": "https://mermaid.live/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057461", "originalId": 220057461, "title": "Untitled diagram | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/9ab0db1d-2aa4-4d0a-945f-037e55c60b94/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057464", "originalId": 220057464, "title": "glossary | Railway", "url": "https://railway.com/project/************************************/service/1fa739df-d725-4abb-9068-74c083b27998/settings?environmentId=23f319c3-14e1-469b-a35c-46a809dda1d0#networking-public", "favicon": "https://railway.com/favicons/favicon-light.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057467", "originalId": 220057467, "title": "diffbot/Llama-3.3-Diffbot-Small-XL-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.3-Diffbot-Small-XL-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057470", "originalId": 220057470, "title": "diffbot/Llama-3.1-Diffbot-Small-2504 · Hugging Face", "url": "https://huggingface.co/diffbot/Llama-3.1-Diffbot-Small-2504", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057473", "originalId": 220057473, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057476", "originalId": 220057476, "title": "Command.new · vibe code any agent | by Langbase", "url": "https://command.new/", "favicon": "https://command.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057479", "originalId": 220057479, "title": "Exa API Dashboard", "url": "https://dashboard.exa.ai/login?redirect=/api-keys", "favicon": "https://dashboard.exa.ai/images/favicon-32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057482", "originalId": 220057482, "title": "Langbase · Serverless AI Developer Platform", "url": "https://langbase.com/", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057485", "originalId": 220057485, "title": "(3) <PERSON> | Act Your Age LIVE Abu Dhabi - YouTube", "url": "https://www.youtube.com/watch?v=0sm75-bdpgQ&t=14s", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057488", "originalId": 220057488, "title": "alt144/glossary at dev", "url": "https://github.com/alt144/glossary/tree/dev", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057738", "originalId": 220057738, "title": "Enregistrez-vous sur votre vol pour Dubai - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox/********************************", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057494", "originalId": 220057494, "title": "Prêt pour l'enregistrement ? Gagnez du temps et enregistrez-vous en ligne - KLM France", "url": "https://www.klm.fr/check-in/passenger-list?checkInGroupId=6a18e04f-7289-4f96-bedd-353db5e55d1b", "favicon": "https://www.static-kl.com/assets/kl/img/logo-48.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220058080", "originalId": 220058080, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057500", "originalId": 220057500, "title": "Shiply France > Visualiser les devis de la Livraison 7 Boxes - 1m3", "url": "https://www.shiply.com/fr/listings/bids/9JXQLXLRA/LANXDDXGQ?h=&h_uid=thom10495&view_bid=LANXDDXGQ&utm_medium=email&utm_source=transactional&utm_campaign=QuoteNotification&utm_nooveride=1&utm_content=Q", "favicon": "https://www.shiply.com/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057503", "originalId": 220057503, "title": "DHL Sendungsverfolgung | DHL", "url": "https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=JJD149990200067579691", "favicon": "https://www.dhl.de/.resources/dhl/webresources/assets/icons/favicons/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057506", "originalId": 220057506, "title": "Shiply France > My Shiply > Active Deliveries", "url": "https://www.shiply.com/fr/listings/user_active", "favicon": "https://www.shiply.com/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057509", "originalId": 220057509, "title": "Large Crypto Cash Out : r/dubai", "url": "https://www.reddit.com/r/dubai/comments/1kbfapg/large_crypto_cash_out/?%24deep_link=true&correlation_id=e4a10265-7d77-41c4-99e2-a9d3a735856b&post_fullname=t3_1kbfapg&post_index=0&ref=email_digest&ref_", "favicon": "https://www.redditstatic.com/shreddit/assets/favicon/64x64.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057512", "originalId": 220057512, "title": "Buy Bitcoin & Crypto | Crypto Exchange, App & Wallet | OKX UAE", "url": "https://www.okx.com/en-ae", "favicon": "https://www.okx.com/cdn/assets/imgs/254/EF6F431DA7FFBCA5.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057515", "originalId": 220057515, "title": "Excalidraw", "url": "https://excalidraw.com/", "favicon": "https://excalidraw.com/favicon-32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057518", "originalId": 220057518, "title": "Page 1 | Untitled | Editor | Retool", "url": "https://corona.retool.com/editor/baa74f3e-5267-11f0-add6-337947c70add/Untitled/page1", "favicon": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMF8yXzI2KSI+CjxtYXNrIGlkPSJtYXNrMF8yXzI2IiBzdHlsZT0ibWFzay10eXBlOmx1bWluYW5jZSIgbWFza1VuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeD0iMCIgeT0iMCIgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiPgo8cGF0aCBkPSJNMTI4IDBIMFYxMjhIMTI4VjBaIiBmaWxsPSJ3aGl0ZSIvPgo8L21hc2s+CjxnIG1hc2s9InVybCgjbWFzazBfMl8yNikiPgo8cGF0aCBkPSJNODAuNjQxNiAxNS42NjY3QzgwLjY0MTYgMTIuOTA1MiA3OC40MDMgMTAuNjY2NiA3NS42NDE2IDEwLjY2NjZIMTguOTc0QzE2LjIxMjYgMTAuNjY2NiAxMy45NzQgMTIuOTA1MiAxMy45NzQgMTUuNjY2N1YzOS4wMDA0QzEzLjk3NCA0MS43NjE4IDE2LjIxMjYgNDQuMDAwNCAxOC45NzQgNDQuMDAwNEg3NS42NDE2Qzc4LjQwMyA0NC4wMDA0IDgwLjY0MTYgNDYuMjM5IDgwLjY0MTYgNDkuMDAwNVY1NS42NjczQzgwLjY0MTYgNTguNDI4NyA3OC40MDMgNjAuNjY3NCA3NS42NDE2IDYwLjY2NzRIMzguOTc0M0MzNi4yMTI5IDYwLjY2NzQgMzMuOTc0MyA2Mi45MDYgMzMuOTc0MyA2NS42Njc0Vjg5LjAwMTFDMzMuOTc0MyA5MS43NjI2IDM2LjIxMjkgOTQuMDAxMiAzOC45NzQzIDk0LjAwMTJINzUuNjQxNkM3OC40MDMgOTQuMDAxMiA4MC42NDE2IDk2LjIzOTggODAuNjQxNiA5OS4wMDEzVjExMi4zMzVDODAuNjQxNiAxMTUuMDk2IDgyLjg4MDMgMTE3LjMzNSA4NS42NDE3IDExNy4zMzVIMTA4Ljk3NUMxMTEuNzM3IDExNy4zMzUgMTEzLjk3NSAxMTUuMDk2IDExMy45NzUgMTEyLjMzNVY4OS4wMDExQzExMy45NzUgODYuMjM5NyAxMTEuNzM3IDg0LjAwMSAxMDguOTc1IDg0LjAwMUg4NS42NDE3QzgyLjg4MDMgODQuMDAxIDgwLjY0MTYgODEuNzYyNCA4MC42NDE2IDc5LjAwMVY3Mi4zMzQyQzgwLjY0MTYgNjkuNTcyNyA4Mi44ODAzIDY3LjMzNDEgODUuNjQxNyA2Ny4zMzQxSDEwOC45NzVDMTExLjczNyA2Ny4zMzQxIDExMy45NzUgNjUuMDk1NSAxMTMuOTc1IDYyLjMzNDFWMzkuMDAwNEMxMTMuOTc1IDM2LjIzODkgMTExLjczNyAzNC4wMDAzIDEwOC45NzUgMzQuMDAwM0g4NS42NDE3QzgyLjg4MDMgMzQuMDAwMyA4MC42NDE2IDMxLjc2MTcgODAuNjQxNiAyOS4wMDAyVjE1LjY2NjdaIiBmaWxsPSIjOTk5OTk5Ii8+CjwvZz4KPC9nPgo8ZGVmcz4KPGNsaXBQYXRoIGlkPSJjbGlwMF8yXzI2Ij4KPHJlY3Qgd2lkdGg9IjEyOCIgaGVpZ2h0PSIxMjgiIGZpbGw9IndoaXRlIi8+CjwvY2xpcFBhdGg+CjwvZGVmcz4KPC9zdmc+Cg==", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057521", "originalId": 220057521, "title": "Watch Best New Movies & Shows from Biggest Studios | OSN+", "url": "https://osnplus.com/en-ae?gad_source=1&gad_campaignid=***********&gbraid=0AAAAABnYLV5z0fQSHtIr0bHl-FtL4oe7M&gclid=CjwKCAjw3_PCBhA2EiwAkH_j4hMF4mhWhPnYZGYFq95esj3UQmD1aDjzi1WP1Y5sxKodfouisetZoRoCnYAQAv", "favicon": "https://osnplus.com/favicon/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057524", "originalId": 220057524, "title": "stanford-oval/storm: An LLM-powered knowledge curation system that researches a topic and generates ", "url": "https://github.com/stanford-oval/storm/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057527", "originalId": 220057527, "title": "apps.abacus.ai/chatllm/?appId=73da811f2", "url": "https://apps.abacus.ai/chatllm/?appId=73da811f2", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057530", "originalId": 220057530, "title": "Cursor - The AI Code Editor", "url": "https://cursor.com/en/agents", "favicon": "https://cursor.com/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057533", "originalId": 220057533, "title": "IP information in prompt risk for AI - IBM Documentation", "url": "https://www.ibm.com/docs/en/watsonx/saas?topic=atlas-ip-information-in-prompt", "favicon": "https://www.ibm.com/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057536", "originalId": 220057536, "title": "dittofeed/dittofeed: Open-source customer engagement. Automate transactional and marketing messages ", "url": "https://github.com/dittofeed/dittofeed", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057539", "originalId": 220057539, "title": "Semantic layer - PandasAI", "url": "https://docs.pandas-ai.com/v3/semantic-layer", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/sinaptik/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057542", "originalId": 220057542, "title": "NL Layer - PandasAI", "url": "https://docs.pandas-ai.com/v3/overview-nl", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/sinaptik/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057545", "originalId": 220057545, "title": "NL2SQL with BigQuery and Gemini | Google Cloud Blog", "url": "https://cloud.google.com/blog/products/data-analytics/nl2sql-with-bigquery-and-gemini", "favicon": "https://www.gstatic.com/cloud/images/icons/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057548", "originalId": 220057548, "title": "Overview | DB-GPT", "url": "http://docs.dbgpt.cn/docs/overview", "favicon": "http://docs.dbgpt.cn/img/eosphoros.jpeg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057551", "originalId": 220057551, "title": "eosphoros-ai/DB-GPT: AI Native Data App Development framework with AWEL(Agentic Workflow Expression ", "url": "https://github.com/eosphoros-ai/DB-GPT?tab=readme-ov-file", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057554", "originalId": 220057554, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_js&utm_medium=editor_selection&utm_campaign=playground#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm3", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057557", "originalId": 220057557, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057560", "originalId": 220057560, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play?utm_source=mermaid_js&utm_medium=editor_selection&utm_campaign=playground#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm3", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057563", "originalId": 220057563, "title": "Mermaid Chart - Create complex, visual diagrams with text. A smarter way of creating diagrams.", "url": "https://www.mermaidchart.com/play#pako:eNp9VO9r2zAQ_VdEoaWDhbGt-5IPgzROi7eYhDpdB-ooiqMkoo5kLLlrGPvfd6cftly3-xCie-_pZJ3u3p-TQm34yfhkV7NqT1bJvSTk9JTM2ZHX5OOYTBtt1AHWqTS8ZoURSpJcNXXBNWp1s_ZbVVPsKyWksTghK", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057566", "originalId": 220057566, "title": "(5) <PERSON> exposes the real risks and rewards of AI - YouTube", "url": "https://www.youtube.com/watch?v=b4e4TwU10YU", "favicon": "https://www.youtube.com/s/desktop/cd5cb204/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057569", "originalId": 220057569, "title": "Chatbase - Pricing", "url": "https://www.chatbase.co/pricing", "favicon": "https://www.chatbase.co/icon.ico?f061e2c391ac8892", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057572", "originalId": 220057572, "title": "Beyond ONE's AI Mandate: From Piloting to Production-Grade Intelligence - Napkin AI", "url": "https://app.napkin.ai/page/CgoiCHByb2Qtb25lEiwKBFBhZ2UaJDg5OGI4NTZmLTRkZjItNDFmMC1iYjNiLTQzNjdhNTc5ODlhMg", "favicon": "https://app.napkin.ai/favicon-16x16.png?v=2", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057575", "originalId": 220057575, "title": "Introduction | dlt Docs", "url": "https://dlthub.com/docs/intro", "favicon": "https://dlthub.com/docs/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057578", "originalId": 220057578, "title": "Google Actualités - Pour vous", "url": "https://news.google.com/foryou?hl=fr&gl=FR&ceid=FR%3Afr", "favicon": "https://lh3.googleusercontent.com/-DR60l-K8vnyi99NZovm9HlXyZwQ85GMDxiwJWzoasZYCUrPuUM_P_4Rb7ei03j-0nRs0c4F=w32", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057581", "originalId": 220057581, "title": "Introduction | dlt Docs", "url": "https://dlthub.com/docs/intro", "favicon": "https://dlthub.com/docs/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057584", "originalId": 220057584, "title": "What is Delta Lake in Databricks? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/delta#:~:text=Delta%20Lake%20is%20the%20optimized,transactions%20and%20scalable%20metadata%20handling.", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057587", "originalId": 220057587, "title": "Integrations | Customer.io", "url": "https://customer.io/integrations", "favicon": "https://customer.io/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057590", "originalId": 220057590, "title": "Meiro CDP - Composable Customer Data Platform", "url": "https://www.meiro.io/", "favicon": "https://www.meiro.io/icon.ico?09524e12951176a5", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057593", "originalId": 220057593, "title": "Dashboard | Fivetran", "url": "https://fivetran.com/pricing-estimator", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057596", "originalId": 220057596, "title": "Google Calendar - August 2025", "url": "https://calendar.google.com/calendar/u/0/r/month", "favicon": "https://calendar.google.com/googlecalendar/images/favicons_2020q4/calendar_15.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057599", "originalId": 220057599, "title": "<PERSON><PERSON>__<PERSON>_Couronne_June_2025.pdf - Google Drive", "url": "https://drive.google.com/file/d/1MVLGIH_geFsdYdaM60yVbE-wClysZC0u/view", "favicon": "https://ssl.gstatic.com/images/branding/product/1x/drive_2020q4_32dp.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057602", "originalId": 220057602, "title": "Presto: Free, Open-Source SQL Query Engine for any Data", "url": "https://prestodb.io/", "favicon": "https://prestodb.io/wp-content/uploads/favicon-150x150.png", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057605", "originalId": 220057605, "title": "About data platform connections | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/cloud/connect-data-platform/about-connections", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057608", "originalId": 220057608, "title": "New Tab", "url": "chrome://newtab/", "favicon": "", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220058083", "originalId": 220058083, "title": "(15) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057612", "originalId": 220057612, "title": "Cultivating Tech Culture", "url": "https://chat.mistral.ai/chat/72c40e13-6892-4284-80fb-d65a85423662", "favicon": "https://chat.mistral.ai/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057615", "originalId": 220057615, "title": "Meiro CDP - Composable Customer Data Platform", "url": "https://www.meiro.io/", "favicon": "https://www.meiro.io/icon.ico?09524e12951176a5", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057618", "originalId": 220057618, "title": "Synthesia Makes AI Video Production Effortless with Generative AI on AWS | Synthesia Case Study | AW", "url": "https://aws.amazon.com/solutions/case-studies/synthesia-case-study/", "favicon": "https://a0.awsstatic.com/libra-css/images/site/fav/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057741", "originalId": 220057741, "title": "actor - Google Search", "url": "https://www.google.com/search?sa=X&sca_esv=adde10145c0e0ebd&lns_surface=44&biw=480&bih=307&hl=en-DE&cs=0&sxsrf=AE3TifPkTBFeQiUuZWaeuQMUva6-HITRmA:1754580070210&udm=24&source=lns.web.cntpubb&vsdim=480,", "favicon": "https://www.gstatic.com/images/branding/searchlogo/ico/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057624", "originalId": 220057624, "title": "claude.ai", "url": "https://claude.ai/api/challenge_redirect?to=https%3A%2F%2Fclaude.ai%2Fchat%2Ff85849a9-2553-435f-9bc2-6d1b3ea3772c", "favicon": "https://claude.ai/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": false, "pinned": false}, {"id": "chrome-220057627", "originalId": 220057627, "title": "write a blog post for internal workvivo talking about a recent news about AI,...", "url": "https://www.perplexity.ai/search/write-a-blog-post-for-internal-cT29sUS0TaGT7335Su8UGA", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": 220057423, "active": true, "pinned": false}, {"id": "chrome-220058222", "originalId": 220058222, "title": "Development & AI Tools", "url": "data:text/html,<html><head><title>Development & AI Tools</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Development & AI Tools</h1><p>This window contains tabs organized by AI.</p></body></html>", "favicon": "", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058205", "originalId": 220058205, "title": "Your Repositories", "url": "https://github.com/alt144?tab=repositories", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": true, "pinned": false}, {"id": "chrome-220058206", "originalId": 220058206, "title": "alt144/glossary", "url": "https://github.com/alt144/glossary", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058207", "originalId": 220058207, "title": "HazyResearch/minions: Big & Small LLMs working together", "url": "https://github.com/hazyResearch/minions/", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058208", "originalId": 220058208, "title": "Automate business processes with AI | Lleverage", "url": "https://www.lleverage.ai/", "favicon": "https://cdn.prod.website-files.com/66db15e9af6f029bc79ce0ac/67d2f0211be93cc2ecbdfbe5_Primary%20Icon.ico", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058209", "originalId": 220058209, "title": "Introducing uv: Next-Gen Python Package Manager | by <PERSON> | Medium", "url": "https://codemaker2016.medium.com/introducing-uv-next-gen-python-package-manager-b78ad39c95d7#8902", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058210", "originalId": 220058210, "title": "Notion Mail", "url": "https://www.notion.com/product/mail", "favicon": "https://www.notion.com/front-static/favicon.ico", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058211", "originalId": 220058211, "title": "mcp-course (Hugging Face MCP Course)", "url": "https://huggingface.co/mcp-course", "favicon": "https://huggingface.co/favicon.ico", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058212", "originalId": 220058212, "title": "Coding-Crashkurse/A2A-LangGraph", "url": "https://github.com/Coding-Crashkurse/A2A-LangGraph", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058213", "originalId": 220058213, "title": "Tsadoq/a2a-mcp-tutorial: A tutorial on how to use Model Context Protocol by Anthropic and Agent2Agen", "url": "https://github.com/Tsadoq/a2a-mcp-tutorial", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058214", "originalId": 220058214, "title": "Interactive Budgeting - There is 1 AI tool For That", "url": "https://theresanaiforthat.com/task/interactive-budgeting/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058215", "originalId": 220058215, "title": "Aider-AI/aider: aider is AI pair programming in your terminal", "url": "https://github.com/Aider-AI/aider", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058216", "originalId": 220058216, "title": "AI APIs for I - TAAFT®", "url": "https://theresanaiforthat.com/apis/s/i/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058217", "originalId": 220058217, "title": "modelcontextprotocol/servers: Model Context Protocol Servers", "url": "https://github.com/modelcontextprotocol/servers", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058218", "originalId": 220058218, "title": "simstudioai/sim: Sim is an open-source AI agent workflow builder. Sim Studio's interface is a lightw", "url": "https://github.com/simstudioai/sim", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058204, "active": false, "pinned": false}, {"id": "chrome-220058252", "originalId": 220058252, "title": "AI Applications & Services", "url": "data:text/html,<html><head><title>AI Applications & Services</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 AI Applications & Services</h1><p>This window contains tabs organized by AI.</p></body>", "favicon": "", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058231", "originalId": 220058231, "title": "Sign up or sign in", "url": "https://login.genspark.ai/gensparkad.onmicrosoft.com/b2c_1_new_login/oauth2/v2.0/authorize?client_id=536a4e98-fd24-4cbc-a67b-417e209e0080&response_type=code&redirect_uri=https%3A%2F%2Fwww.genspark.ai%", "favicon": "https://www.genspark.ai/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": true, "pinned": false}, {"id": "chrome-220058232", "originalId": 220058232, "title": "Context Engineering Platform for AI Agents - Zep", "url": "https://www.getzep.com/", "favicon": "https://www.getzep.com/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058233", "originalId": 220058233, "title": "BaseAI - The first Web AI Framework", "url": "https://baseai.dev/", "favicon": "https://baseai.dev/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058234", "originalId": 220058234, "title": "thomascouronne - Home", "url": "https://lightning.ai/thomascouronne/home", "favicon": "https://lightning.ai/favicon/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058235", "originalId": 220058235, "title": "Perplexity", "url": "https://www.perplexity.ai/?login-source=oneTapHome&login-new=false", "favicon": "https://www.perplexity.ai/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058236", "originalId": 220058236, "title": "Page not found | Pecan AI", "url": "https://www.pecan.ai/home-2-1/", "favicon": "https://www.pecan.ai/wp-content/uploads/2023/11/favicon.svg", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058237", "originalId": 220058237, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/?r=0", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058238", "originalId": 220058238, "title": "openinterpreter/open-interpreter: A natural language interface for computers", "url": "https://github.com/OpenInterpreter/open-interpreter", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058242", "originalId": 220058242, "title": "AI APIs for I - TAAFT®", "url": "https://theresanaiforthat.com/apis/s/i/", "favicon": "https://theresanaiforthat.com/favicon-32x32.png", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058243", "originalId": 220058243, "title": "Borderless AI & Cohere Launch HR AI Agent | Cohere", "url": "https://cohere.com/customer-stories/borderless-ai", "favicon": "https://cohere.com/favicon-32x32.png", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058246", "originalId": 220058246, "title": "Pythonic, Modern Workflow Orchestration For Resilient Data Platforms | Prefect", "url": "https://www.prefect.io/", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058249", "originalId": 220058249, "title": "claude.ai/chat/f85849a9-2553-435f-9bc2-6d1b3ea3772c", "url": "https://claude.ai/chat/f85849a9-2553-435f-9bc2-6d1b3ea3772c", "favicon": "https://claude.ai/favicon.ico", "browser": "chrome", "windowId": 220058229, "active": false, "pinned": false}, {"id": "chrome-220058274", "originalId": 220058274, "title": "Data Management & Analytics", "url": "data:text/html,<html><head><title>Data Management & Analytics</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Data Management & Analytics</h1><p>This window contains tabs organized by AI.</p></bod", "favicon": "", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058260", "originalId": 220058260, "title": "What is the Databricks CLI? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/dev-tools/cli", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058261", "originalId": 220058261, "title": "Create tables  |  Dataform  |  Google Cloud", "url": "https://cloud.google.com/dataform/docs/create-tables", "favicon": "https://www.gstatic.com/devrel-devsite/prod/v80eb94e0352d656ad1e20abf6117cdec6c1343c7722ef10f52a1a3f77f1e58f7/cloud/images/favicons/onecloud/favicon.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058268", "originalId": 220058268, "title": "Top Open Source ETL Frameworks in 2025 + Alternatives | Integrate.io", "url": "https://www.integrate.io/blog/open-source-etl-frameworks-revolutionizing-data-integration/", "favicon": "https://www.integrate.io/blog/assets/favicon-7b3fac9c5056e956d9a9c853a88d9979229875e342663df97bd54b2eecee2827.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058269", "originalId": 220058269, "title": "Dremio Apache Iceberg Solutions and Capabilities", "url": "https://www.dremio.com/platform/apache-iceberg/", "favicon": "https://www.dremio.com//wp-content/themes/subsurface/assets/_corp/favicon.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058270", "originalId": 220058270, "title": "About MetricFlow | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/build/about-metricflow", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058271", "originalId": 220058271, "title": "What is Delta Lake in Databricks? | Databricks Documentation", "url": "https://docs.databricks.com/aws/en/delta#:~:text=Delta%20Lake%20is%20the%20optimized,transactions%20and%20scalable%20metadata%20handling.", "favicon": "https://docs.databricks.com/aws/en/img/favicon.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058272", "originalId": 220058272, "title": "About data platform connections | dbt Developer Hub", "url": "https://docs.getdbt.com/docs/cloud/connect-data-platform/about-connections", "favicon": "https://docs.getdbt.com/img/favicon.svg", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058273", "originalId": 220058273, "title": "Architecting Global Data Collaboration with Delta Sharing | Databricks Blog", "url": "https://www.databricks.com/blog/architecting-global-data-collaboration-delta-sharing", "favicon": "https://www.databricks.com/en-blog-assets/favicon-32x32.png?v=c9b9916c3b27dc51866c46b79a6e9b88", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058478", "originalId": 220058478, "title": "Extensions", "url": "chrome://extensions/", "favicon": "", "browser": "chrome", "windowId": 220058259, "active": true, "pinned": false}, {"id": "chrome-220058481", "originalId": 220058481, "title": "Visual Studio Code on Linux", "url": "https://code.visualstudio.com/docs/setup/linux", "favicon": "https://code.visualstudio.com/assets/favicon.ico", "browser": "chrome", "windowId": 220058259, "active": false, "pinned": false}, {"id": "chrome-220058342", "originalId": 220058342, "title": "Documentation & Guides", "url": "data:text/html,<html><head><title>Documentation & Guides</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Documentation & Guides</h1><p>This window contains tabs organized by AI.</p></body></html>", "favicon": "", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058290", "originalId": 220058290, "title": "Ma<PERSON>wl<PERSON>", "url": "https://www.magnowlia.com/", "favicon": "https://www.magnowlia.com/wp-content/uploads/2024/04/cropped-Magnowlia_logo_slack_blue_darksand-1-32x32.png", "browser": "chrome", "windowId": 220058289, "active": true, "pinned": false}, {"id": "chrome-220058291", "originalId": 220058291, "title": "ag-ui-protocol/ag-ui: AG-UI: the Agent-User Interaction Protocol. Bring Agents into Frontend Applica", "url": "https://github.com/ag-ui-protocol/ag-ui", "favicon": "https://github.githubassets.com/favicons/favicon.svg", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058292", "originalId": 220058292, "title": "Oracle Cloud Free Tier Signup", "url": "https://signup.cloud.oracle.com/?verify_email=eyJhbGciOiJIUzI1NiJ9.eyJjYXB0Y2hhSnd0VG9rZW4iOiJleUpoYkdjaU9pSklVekkxTmlKOS5leUp6ZFdJaU9pSnZZMmt0YzJsbmJuVndJaXdpYVhOeklqb2lhSFIwY0hNNkx5OXphV2R1ZFhBdWIzS", "favicon": "https://signup.cloud.oracle.com/oraclefavicon.ico", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058293", "originalId": 220058293, "title": "Pricing | Prefect Cloud", "url": "https://www.prefect.io/pricing", "favicon": "https://www.prefect.io/favicon.ico", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058294", "originalId": 220058294, "title": "Power BI: Pricing Plan | Microsoft Power Platform", "url": "https://www.microsoft.com/en/power-platform/products/power-bi/pricing?market=af#tabs-pill-bar-ocbbe94_tab1", "favicon": "https://www.microsoft.com/favicon.ico?v2", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058301", "originalId": 220058301, "title": "Transition | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/3d265b3f-8939-491d-a215-a45bf06c8729/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058304", "originalId": 220058304, "title": "To-be | Mermaid Chart", "url": "https://www.mermaidchart.com/app/projects/458b8a0f-b962-4a08-a51a-2f0b1c05b8c5/diagrams/2dc7e96c-c083-419d-948e-f24c7f8d9883/version/v0.1/edit", "favicon": "https://www.mermaidchart.com/img/favicon.ico", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058316", "originalId": 220058316, "title": "Kilo Code Documentation | Kilo Code Docs", "url": "https://kilocode.ai/docs/", "favicon": "https://kilocode.ai/docs/img/favicon.ico", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058319", "originalId": 220058319, "title": "Install and Upgrade - HVR 6 | Fivetran Documentation", "url": "https://fivetran.com/docs/hvr6/install-and-upgrade", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058341", "originalId": 220058341, "title": "HVR 6 Documentation | Fivetran Documentation", "url": "https://fivetran.com/docs/hvr6", "favicon": "https://fivetran.com/favicon.png", "browser": "chrome", "windowId": 220058289, "active": false, "pinned": false}, {"id": "chrome-220058360", "originalId": 220058360, "title": "Communication & Collaboration", "url": "data:text/html,<html><head><title>Communication & Collaboration</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Communication & Collaboration</h1><p>This window contains tabs organized by AI.</p><", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058352", "originalId": 220058352, "title": "(17) Feed | LinkedIn", "url": "https://www.linkedin.com/feed/", "favicon": "https://static.licdn.com/aero-v1/sc/h/3loy7tajf3n0cho89wgg0fjre", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220058353", "originalId": 220058353, "title": "Inbox (73) - <EMAIL> - Gmail", "url": "https://mail.google.com/mail/u/0/#inbox", "favicon": "https://ssl.gstatic.com/ui/v1/icons/mail/rfr/unreadcountfavicon/3/70+_2x.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058354", "originalId": 220058354, "title": "XLSTAT | Statistical Software for Excel", "url": "https://www.xlstat.com/", "favicon": "https://cdn.xlstat.com/dist/assets/img/favicon.svg?v=1d844", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058355", "originalId": 220058355, "title": "ChatLLM - AI Chat And Agents on the App Store", "url": "https://apps.apple.com/us/app/chatllm-ai-chat-and-agents/id6502844681", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058356", "originalId": 220058356, "title": "ChatLLM Teams", "url": "https://apps.abacus.ai/chatllm/?appId=appllm_engineer", "favicon": "https://abacus.ai/static/imgs/hp_chat_llm.webp", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058357", "originalId": 220058357, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fverify%3Fresponse%3DeyJ1c2VyIjp7ImlkIjoid0Y0NmJOblF2YVFxd01ZTk5GdGZBUiIsIm5hbWUiOiJUaG9tYXMgQ291cm9ubsOpIiwiZW1haWwiOiJ0aG9tYXMuY291cm9ubmVAYmV5b25kLm9uZSIsIm", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Segment", "url": "https://app.segment.com/login?redirect=%2Fbeyond-thomas-couronne%2Fintegration-health", "favicon": "https://d1gi394wp2tyv2.cloudfront.net/app/hashed-v6.1/static/svg/favicon.5bd0a77c.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "<PERSON><PERSON><PERSON><PERSON> Account", "url": "https://account.hubstaff.com/confirmation_sent/PgGfHajmIex+ciZHrQ7baLyM--IAkusBD5Oh3v7BTc--P3ikljwtIWa2GFDqs4mLeA==", "favicon": "https://account-assets.hubstaff.com/864c8aa8/favicon/favicon-521b5b5302434bea83e1aee91bedca029db84b4786fa33db3f973717359da2a1.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Travel & Logistics", "url": "data:text/html,<html><head><title>Travel & Logistics</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Travel & Logistics</h1><p>This window contains tabs organized by AI.</p></body></html>", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "HEIMOANA Current position (Pleasure craft, MMSI *********) - VesselFinder", "url": "https://www.vesselfinder.com/?mmsi=*********", "favicon": "https://www.vesselfinder.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Dubai to Berlin | Google Flights", "url": "https://www.google.com/travel/flights/search?tfs=CBwQAhopEgoyMDI1LTA2LTA1ag0IAhIJL20vMDFmMDhycgwIAxIIL20vMDE1NnEaKRIKMjAyNS0wNi0wOWoMCAMSCC9tLzAxNTZxcg0IAhIJL20vMDFmMDhyQAFIAXABggELCP___________wGYAQE", "favicon": "https://www.gstatic.com/images/branding/product/2x/travel_flights_48dp.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Telr | Accept Payments Online | Payment Gateway Provider in UAE.", "url": "https://telr.com/?r=0", "favicon": "https://cdn.prod.website-files.com/65fa7e78751459f6afa1496f/667d1c80e120d97756295572_favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Shiply France > Visualiser les devis de la Livraison 7 Boxes - 1m3", "url": "https://www.shiply.com/fr/listings/bids/9JXQLXLRA/LANXDDXGQ?h=&h_uid=thom10495&view_bid=LANXDDXGQ&utm_medium=email&utm_source=transactional&utm_campaign=QuoteNotification&utm_nooveride=1&utm_content=Q", "favicon": "https://www.shiply.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "DHL Sendungsverfolgung | DHL", "url": "https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=JJD149990200067579691", "favicon": "https://www.dhl.de/.resources/dhl/webresources/assets/icons/favicons/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058408", "originalId": 220058408, "title": "Finance & Budgeting", "url": "data:text/html,<html><head><title>Finance & Budgeting</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Finance & Budgeting</h1><p>This window contains tabs organized by AI.</p></body></html>", "favicon": "", "browser": "chrome", "windowId": 220058379, "active": false, "pinned": false}, {"id": "chrome-220058380", "originalId": 220058380, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/oauth/code/success?app=claude-code", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": 220058379, "active": true, "pinned": false}, {"id": "chrome-220058381", "originalId": 220058381, "title": "sda814a.evoshosting.com", "url": "http://sda814a.evoshosting.com/", "favicon": "", "browser": "chrome", "windowId": 220058379, "active": false, "pinned": false}, {"id": "chrome-220058382", "originalId": 220058382, "title": "About BeyondTrust Privileged Access Management | BeyondTrust", "url": "https://www.beyondtrust.com/brand?utm_source=google&utm_medium=cpc&utm_campaign=9336390457&utm_content=************&utm_term=beyond%20trust&gad_source=1&gad_campaignid=9336390457&gbraid=0AAAAACjgS9pCF", "favicon": "https://www.beyondtrust.com/favicon.png?v=5", "browser": "chrome", "windowId": 220058379, "active": false, "pinned": false}, {"id": "chrome-220058389", "originalId": 220058389, "title": "Claude <PERSON> overview - Anthropic", "url": "https://docs.anthropic.com/en/docs/claude-code/overview", "favicon": "https://mintlify.s3-us-west-1.amazonaws.com/anthropic/_generated/favicon/favicon.ico?v=3", "browser": "chrome", "windowId": 220058379, "active": false, "pinned": false}, {"id": "chrome-220058407", "originalId": 220058407, "title": "Plum 101: Your Questions Answered", "url": "https://blog.withplum.com/plum-user-questions-answered/", "favicon": "https://blog.withplum.com/content/images/size/w256h256/2019/07/img_plum_avatar.png", "browser": "chrome", "windowId": 220058379, "active": false, "pinned": false}, {"id": "chrome-220058422", "originalId": 220058422, "title": "Research & Learning", "url": "data:text/html,<html><head><title>Research & Learning</title></head><body><h1>%C3%B0%C5%B8%E2%80%9C%C2%81 Research & Learning</h1><p>This window contains tabs organized by AI.</p></body></html>", "favicon": "", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058410", "originalId": 220058410, "title": "Command.new · vibe code any agent | by Langbase", "url": "https://command.new/", "favicon": "https://command.new/favicon/2024/favicon-32x32.png", "browser": "chrome", "windowId": *********, "active": true, "pinned": false}, {"id": "chrome-220058411", "originalId": 220058411, "title": "Outlook for macOS MCP Server by <PERSON> | PulseMCP", "url": "https://www.pulsemcp.com/servers/syedazharmbnr1-outlook-for-macos", "favicon": "https://www.pulsemcp.com/favicon.svg", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058412", "originalId": 220058412, "title": "Customer Stories | Hightouch", "url": "https://hightouch.com/customers", "favicon": "https://hightouch.com/favicon.ico?v=3", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-220058418", "originalId": 220058418, "title": "Microsoft Teams Meets Databricks Genie API: A Complete Setup Guide | by <PERSON> | Medium", "url": "https://medium.com/@ryan-bates/microsoft-teams-meets-databricks-genie-api-a-complete-setup-guide-81f629ace634", "favicon": "https://miro.medium.com/v2/5d8de952517e8160e40ef9841c781cdc14a5db313057fa3c3de41c6f5b494b19", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Anthrop<PERSON> Console", "url": "https://console.anthropic.com/login?selectAccount=true&returnTo=%2Foauth%2Fauthorize%3Fcode%3Dtrue%26client_id%3D9d1c250a-e61b-44d9-88ed-5944d1962f5e%26response_type%3Dcode%26redirect_uri%3Dhttp%253A%", "favicon": "https://console.anthropic.com/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "Menlo Research", "url": "https://menlo.ai/", "favicon": "https://menlo.ai/favicon.ico", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}, {"id": "chrome-*********", "originalId": *********, "title": "<PERSON> exposes the real risks and rewards of AI - YouTube", "url": "https://www.youtube.com/watch?v=b4e4TwU10YU", "favicon": "https://www.youtube.com/s/desktop/c81c827c/img/logos/favicon_32x32.png", "browser": "chrome", "windowId": *********, "active": false, "pinned": false}]}