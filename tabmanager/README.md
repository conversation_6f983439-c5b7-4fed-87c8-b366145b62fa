# Tab Manager

A standalone desktop application that allows you to view, search, and manage tabs from both Chrome and Edge browsers in one unified interface.

## Features

- 📋 **Unified Tab List**: View all open tabs from Chrome and Edge in one place
- 🔍 **Search Functionality**: Search tabs by title or URL
- 🗂️ **Sorting Options**: Sort tabs by title, URL, or browser
- 🎯 **Filter by Browser**: Show tabs from specific browsers
- ❌ **Close Tabs**: Close tabs directly from the app
- 🔄 **Real-time Updates**: Automatically updates when tabs are opened/closed
- 🎨 **Modern UI**: Clean, responsive interface built with Electron

## Architecture

The Tab Manager consists of three main components:

1. **Desktop App** (Electron): The main user interface
2. **Browser Extensions**: Lightweight extensions for Chrome and Edge that expose tab data
3. **Native Messaging Host**: Bridge between the desktop app and browser extensions

## Installation

### Prerequisites

- Node.js (v16 or higher)
- Chrome and/or Edge browsers

### Step 1: Install Dependencies

```bash
cd tabmanager
npm install
```

### Step 2: Install Native Messaging Host

```bash
cd native-host
node install.js install
```

This will install the native messaging host that allows communication between the desktop app and browser extensions.

### Step 3: Install Browser Extensions

#### Chrome Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the `extensions/chrome` folder
4. Note the extension ID that appears

#### Edge Extension
1. Open Edge and go to `edge://extensions/`
2. Enable "Developer mode" in the left sidebar
3. Click "Load unpacked" and select the `extensions/edge` folder
4. Note the extension ID that appears

### Step 4: Update Extension IDs (Important!)

Edit `native-host/install.js` and update the `getExtensionId()` method with the actual extension IDs from step 3, then reinstall:

```bash
cd native-host
node install.js uninstall
node install.js install
```

## Usage

### Start the Application

```bash
npm start
```

### Using the App

1. **View Tabs**: All open tabs from Chrome and Edge will be displayed
2. **Search**: Use the search box to filter tabs by title or URL
3. **Sort**: Use the dropdown to sort tabs by different criteria
4. **Filter**: Click browser buttons to show tabs from specific browsers
5. **Close Tabs**: Click the ❌ button next to any tab to close it
6. **Refresh**: Click the refresh button to manually update the tab list

## Development

### Run in Development Mode

```bash
npm run dev
```

### Build for Distribution

```bash
# Build for current platform
npm run build

# Build for specific platforms
npm run build-win    # Windows
npm run build-mac    # macOS
npm run build-linux  # Linux
```

## Troubleshooting

### Extension Not Connecting

1. Check that the native messaging host is installed correctly
2. Verify the extension IDs in the native host manifest
3. Check browser console for error messages
4. Look at the native host log file: `native-host/host.log`

### No Tabs Showing

1. Make sure Chrome/Edge is running with tabs open
2. Check that the browser extensions are installed and enabled
3. Try refreshing the tab list
4. Restart the desktop app

### Permission Issues

- On macOS/Linux: Make sure the native host script is executable (`chmod +x native-host/host.js`)
- On Windows: Run the registry commands as Administrator

## Technical Details

### Native Messaging Protocol

The app uses Chrome's native messaging protocol to communicate with browser extensions. Messages are exchanged in JSON format with a 4-byte length header.

### Security

- Extensions only have access to tab information (title, URL, favicon)
- No access to page content or sensitive data
- Communication is local-only between the desktop app and browsers

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details
