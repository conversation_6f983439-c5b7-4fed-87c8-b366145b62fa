{"name": "electron-builder-squirrel-windows", "version": "24.13.3", "main": "out/SquirrelWindowsTarget.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/electron-userland/electron-builder.git", "directory": "packages/electron-builder-squirrel-windows"}, "bugs": "https://github.com/electron-userland/electron-builder/issues", "homepage": "https://github.com/electron-userland/electron-builder", "files": ["out"], "dependencies": {"archiver": "^5.3.1", "fs-extra": "^10.1.0", "app-builder-lib": "24.13.3", "builder-util": "24.13.1"}, "devDependencies": {"@types/archiver": "5.3.1", "@types/fs-extra": "9.0.13"}, "types": "./out/SquirrelWindowsTarget.d.ts"}