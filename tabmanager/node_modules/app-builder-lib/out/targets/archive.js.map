{"version": 3, "file": "archive.js", "sourceRoot": "", "sources": ["../../src/targets/archive.ts"], "names": [], "mappings": ";;;AAAA,+CAAiD;AACjD,4CAAwE;AACxE,uCAA+B;AAC/B,6BAA4B;AAC5B,6BAAwD;AAGxD,mCAA2C;AAC3C,+CAAyC;AAEzC,gBAAgB;AACT,KAAK,UAAU,GAAG,CAAC,WAAmC,EAAE,MAAc,EAAE,OAAe,EAAE,YAAoB,EAAE,QAAiB,EAAE,cAAsB;IAC7J,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;IACpE,MAAM,OAAO,GAAgC;QAC3C,IAAI,EAAE,OAAO;QACb,QAAQ,EAAE,IAAI;QACd,GAAG,EAAE,YAAY;QACjB,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,MAAM,EAAE,CAAC;KAC7C,CAAA;IACD,IAAI,YAAY,GAAG,GAAG,CAAA;IACtB,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,OAAO,CAAC,MAAM,CAAA;QACrB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QACxC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;IAC5C,CAAC;IAED,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,IAAA,YAAM,EAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;QAC/B,6DAA6D;QAC7D,IAAA,mBAAc,EAAC,OAAO,CAAC;KACxB,CAAC,CAAA;IAEF,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;QACxB,uCAAuC;QACvC,IAAI,QAAQ,GAAG,MAAM,CAAA;QACrB,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAA,yBAAiB,GAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QAClE,CAAC;QACD,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,QAAQ,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,CAAA;QACtH,6GAA6G;QAC7G,MAAM,IAAA,eAAI,EAAC,GAAG,OAAO,KAAK,EAAE,OAAO,CAAC,CAAA;QACpC,OAAM;IACR,CAAC;IAED,MAAM,IAAI,GAAG,qBAAqB,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE;QACvG,aAAa,EAAE,IAAI;QACnB,MAAM,EAAE,SAAS;QACjB,WAAW;KACZ,CAAC,CAAA;IACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;IAC3B,MAAM,IAAA,mBAAI,EACR,MAAM,IAAA,yBAAU,GAAE,EAClB,IAAI,EACJ;QACE,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;KAChC,EACD,sBAAO,CAAC,OAAO,CAChB,CAAA;AACH,CAAC;AA/CD,kBA+CC;AA6BD,SAAgB,qBAAqB,CAAC,MAAc,EAAE,UAA0B,EAAE;IAChF,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW,KAAK,OAAO,CAAA;IAC/C,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CAAC,CAAA;IAE7B,IAAI,UAAU,GAAG,KAAK,CAAA;IACtB,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,IAAI,EAAE,CAAC;QAC3D,SAAS,GAAG,KAAK,CAAA;QACjB,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,CAAC,CAAA;QAClE,UAAU,GAAG,IAAI,CAAA;IACnB,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,KAAK,KAAK,CAAA;IAC9B,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,IAAI,KAAK,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YAC/C,gCAAgC;YAChC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QACpC,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,kEAAkE;YAClE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QAC/E,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAA;IACvC,CAAC;IAED,8DAA8D;IAC9D,mGAAmG;IACnG,4EAA4E;IAC5E,gJAAgJ;IAChJ,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IACvB,CAAC;IAED,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9C,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACtB,CAAC;QAED,IAAI,OAAO,CAAC,yBAAyB,KAAK,KAAK,EAAE,CAAC;YAChD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACvB,CAAC;QAED,gCAAgC;QAChC,uDAAuD;QACvD,IAAI,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAAA;QAC5D,CAAC;QAED,yBAAyB;QACzB,gEAAgE;QAChE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;IACnC,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC,IAAI,CAAC,OAAO,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,IAAI,SAAS,EAAE,CAAC;QAC9B,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAA;IACpD,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACV,kEAAkE;QAClE,mGAAmG;QACnG,2DAA2D;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACnB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAvED,sDAuEC;AAED,SAAgB,sBAAsB,CAAC,UAA0B,EAAE;IACjE,IAAI,SAAS,GAAG,OAAO,CAAC,WAAW,KAAK,OAAO,CAAA;IAC/C,wBAAwB;IACxB,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAC/B,IAAI,sBAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjB,CAAC;IAED,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,IAAI,IAAI,EAAE,CAAC;QAC3D,SAAS,GAAG,KAAK,CAAA;QACjB,IAAI,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,CAAC,CAAA;IACjE,CAAC;SAAM,IAAI,CAAC,SAAS,EAAE,CAAC;QACtB,kEAAkE;QAClE,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;IAClE,CAAC;IAED,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QAC7B,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,EAAE,6BAA6B,CAAC,CAAA;IACzE,CAAC;IAED,kGAAkG;IAClG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACjB,CAAC;IAED,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAC3B,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,EAAE,6BAA6B,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IAClD,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAjCD,wDAiCC;AAED,6CAA6C;AAC7C,gBAAgB;AACT,KAAK,UAAU,OAAO,CAAC,MAAc,EAAE,OAAe,EAAE,YAAoB,EAAE,UAA0B,EAAE;IAC/G,MAAM,WAAW,GAAG,MAAM,IAAA,eAAU,EAAC,OAAO,CAAC,CAAA;IAC7C,MAAM,OAAO,GAAG,MAAM,IAAA,eAAU,EAAC,YAAY,CAAC,CAAA;IAC9C,IAAI,WAAW,IAAI,OAAO,IAAI,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QAChE,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,4BAA4B,EAAE,OAAO,EAAE,EAAE,mBAAmB,CAAC,CAAA;QAChF,OAAO,OAAO,CAAA;IAChB,CAAC;IACD,IAAI,KAAK,GAAG,IAAI,CAAA;IAChB,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,IAAI,MAAM,KAAK,KAAK,IAAI,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,YAAY,EAAE,CAAC;QACxG,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6CAA6C,EAAE,EAAE,WAAW,CAAC,CAAA;QAChF,KAAK,GAAG,KAAK,CAAA;IACf,CAAC;IACD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAA;IAC7F,qEAAqE;IACrE,MAAM,IAAA,mBAAc,EAAC,OAAO,CAAC,CAAA;IAE7B,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAA;IAC1E,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;QAC7B,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;IAED,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,MAAM,IAAA,yBAAU,GAAE,CAAC,CAAC,CAAC,KAAK,CAAA;QACjD,MAAM,IAAA,mBAAI,EACR,MAAM,EACN,IAAI,EACJ;YACE,GAAG,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;SACpE,EACD,sBAAO,CAAC,OAAO,CAChB,CAAA;IACH,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,MAAM,IAAA,WAAM,EAAC,YAAY,CAAC,CAAC,EAAE,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,2BAA2B,YAAY,iBAAiB,CAAC,CAAA;QAC3E,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,CAAA;QACT,CAAC;IACH,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AA1CD,0BA0CC;AAED,SAAS,WAAW,CAAC,OAAkB;IACrC,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;IAC7B,IAAI,sBAAO,CAAC,OAAO,EAAE,CAAC;QACpB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { debug7z, exec, log } from \"builder-util\"\nimport { exists, unlinkIfExists, statOrNull } from \"builder-util/out/fs\"\nimport { move } from \"fs-extra\"\nimport * as path from \"path\"\nimport { create, CreateOptions, FileOptions } from \"tar\"\nimport { TmpDir } from \"temp-file\"\nimport { CompressionLevel } from \"../core\"\nimport { getLinuxToolsPath } from \"./tools\"\nimport { getPath7za } from \"builder-util\"\n\n/** @internal */\nexport async function tar(compression: CompressionLevel | any, format: string, outFile: string, dirToArchive: string, isMacApp: boolean, tempDirManager: TmpDir): Promise<void> {\n  const tarFile = await tempDirManager.getTempFile({ suffix: \".tar\" })\n  const tarArgs: CreateOptions & FileOptions = {\n    file: tarFile,\n    portable: true,\n    cwd: dirToArchive,\n    prefix: path.basename(outFile, `.${format}`),\n  }\n  let tarDirectory = \".\"\n  if (isMacApp) {\n    delete tarArgs.prefix\n    tarArgs.cwd = path.dirname(dirToArchive)\n    tarDirectory = path.basename(dirToArchive)\n  }\n\n  await Promise.all([\n    create(tarArgs, [tarDirectory]),\n    // remove file before - 7z doesn't overwrite file, but update\n    unlinkIfExists(outFile),\n  ])\n\n  if (format === \"tar.lz\") {\n    // noinspection SpellCheckingInspection\n    let lzipPath = \"lzip\"\n    if (process.platform === \"darwin\") {\n      lzipPath = path.join(await getLinuxToolsPath(), \"bin\", lzipPath)\n    }\n    await exec(lzipPath, [compression === \"store\" ? \"-1\" : \"-9\", \"--keep\" /* keep (don't delete) input files */, tarFile])\n    // bloody lzip creates file in the same dir where input file with postfix `.lz`, option --output doesn't work\n    await move(`${tarFile}.lz`, outFile)\n    return\n  }\n\n  const args = compute7zCompressArgs(format === \"tar.xz\" ? \"xz\" : format === \"tar.bz2\" ? \"bzip2\" : \"gzip\", {\n    isRegularFile: true,\n    method: \"DEFAULT\",\n    compression,\n  })\n  args.push(outFile, tarFile)\n  await exec(\n    await getPath7za(),\n    args,\n    {\n      cwd: path.dirname(dirToArchive),\n    },\n    debug7z.enabled\n  )\n}\n\nexport interface ArchiveOptions {\n  compression?: CompressionLevel | null\n\n  /**\n   * @default false\n   */\n  withoutDir?: boolean\n\n  /**\n   * @default true\n   */\n  solid?: boolean\n\n  /**\n   * @default true\n   */\n  isArchiveHeaderCompressed?: boolean\n\n  dictSize?: number\n  excluded?: Array<string> | null\n\n  // DEFAULT allows to disable custom logic and do not pass method switch at all\n  method?: \"Copy\" | \"LZMA\" | \"Deflate\" | \"DEFAULT\"\n\n  isRegularFile?: boolean\n}\n\nexport function compute7zCompressArgs(format: string, options: ArchiveOptions = {}) {\n  let storeOnly = options.compression === \"store\"\n  const args = debug7zArgs(\"a\")\n\n  let isLevelSet = false\n  if (process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL != null) {\n    storeOnly = false\n    args.push(`-mx=${process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL}`)\n    isLevelSet = true\n  }\n\n  const isZip = format === \"zip\"\n  if (!storeOnly) {\n    if (isZip && options.compression === \"maximum\") {\n      // http://superuser.com/a/742034\n      args.push(\"-mfb=258\", \"-mpass=15\")\n    }\n\n    if (!isLevelSet) {\n      // https://github.com/electron-userland/electron-builder/pull/3032\n      args.push(\"-mx=\" + (!isZip || options.compression === \"maximum\" ? \"9\" : \"7\"))\n    }\n  }\n\n  if (options.dictSize != null) {\n    args.push(`-md=${options.dictSize}m`)\n  }\n\n  // https://sevenzip.osdn.jp/chm/cmdline/switches/method.htm#7Z\n  // https://stackoverflow.com/questions/27136783/7zip-produces-different-output-from-identical-input\n  // tc and ta are off by default, but to be sure, we explicitly set it to off\n  // disable \"Stores NTFS timestamps for files: Modification time, Creation time, Last access time.\" to produce the same archive for the same data\n  if (!options.isRegularFile) {\n    args.push(\"-mtc=off\")\n  }\n\n  if (format === \"7z\" || format.endsWith(\".7z\")) {\n    if (options.solid === false) {\n      args.push(\"-ms=off\")\n    }\n\n    if (options.isArchiveHeaderCompressed === false) {\n      args.push(\"-mhc=off\")\n    }\n\n    // https://www.7-zip.org/7z.html\n    // Filters: BCJ, BCJ2, ARM, ARMT, IA64, PPC, SPARC, ...\n    if (process.env.ELECTRON_BUILDER_7Z_FILTER) {\n      args.push(`-mf=${process.env.ELECTRON_BUILDER_7Z_FILTER}`)\n    }\n\n    // args valid only for 7z\n    // -mtm=off disable \"Stores last Modified timestamps for files.\"\n    args.push(\"-mtm=off\", \"-mta=off\")\n  }\n\n  if (options.method != null) {\n    if (options.method !== \"DEFAULT\") {\n      args.push(`-mm=${options.method}`)\n    }\n  } else if (isZip || storeOnly) {\n    args.push(`-mm=${storeOnly ? \"Copy\" : \"Deflate\"}`)\n  }\n\n  if (isZip) {\n    // -mcu switch:  7-Zip uses UTF-8, if there are non-ASCII symbols.\n    // because default mode: 7-Zip uses UTF-8, if the local code page doesn't contain required symbols.\n    // but archive should be the same regardless where produced\n    args.push(\"-mcu\")\n  }\n  return args\n}\n\nexport function computeZipCompressArgs(options: ArchiveOptions = {}) {\n  let storeOnly = options.compression === \"store\"\n  // do not deref symlinks\n  const args = [\"-q\", \"-r\", \"-y\"]\n  if (debug7z.enabled) {\n    args.push(\"-v\")\n  }\n\n  if (process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL != null) {\n    storeOnly = false\n    args.push(`-${process.env.ELECTRON_BUILDER_COMPRESSION_LEVEL}`)\n  } else if (!storeOnly) {\n    // https://github.com/electron-userland/electron-builder/pull/3032\n    args.push(\"-\" + (options.compression === \"maximum\" ? \"9\" : \"7\"))\n  }\n\n  if (options.dictSize != null) {\n    log.warn({ distSize: options.dictSize }, `ignoring unsupported option`)\n  }\n\n  // do not save extra file attributes (Extended Attributes on OS/2, uid/gid and file times on Unix)\n  if (!options.isRegularFile) {\n    args.push(\"-X\")\n  }\n\n  if (options.method != null) {\n    if (options.method !== \"DEFAULT\") {\n      log.warn({ method: options.method }, `ignoring unsupported option`)\n    }\n  } else {\n    args.push(\"-Z\", storeOnly ? \"store\" : \"deflate\")\n  }\n  return args\n}\n\n// 7z is very fast, so, use ultra compression\n/** @internal */\nexport async function archive(format: string, outFile: string, dirToArchive: string, options: ArchiveOptions = {}): Promise<string> {\n  const outFileStat = await statOrNull(outFile)\n  const dirStat = await statOrNull(dirToArchive)\n  if (outFileStat && dirStat && outFileStat.mtime > dirStat.mtime) {\n    log.info({ reason: \"Archive file is up to date\", outFile }, `skipped archiving`)\n    return outFile\n  }\n  let use7z = true\n  if (process.platform === \"darwin\" && format === \"zip\" && dirToArchive.normalize(\"NFC\") !== dirToArchive) {\n    log.warn({ reason: \"7z doesn't support NFD-normalized filenames\" }, `using zip`)\n    use7z = false\n  }\n  const args = use7z ? compute7zCompressArgs(format, options) : computeZipCompressArgs(options)\n  // remove file before - 7z and zip doesn't overwrite file, but update\n  await unlinkIfExists(outFile)\n\n  args.push(outFile, options.withoutDir ? \".\" : path.basename(dirToArchive))\n  if (options.excluded != null) {\n    for (const mask of options.excluded) {\n      args.push(use7z ? `-xr!${mask}` : `-x${mask}`)\n    }\n  }\n\n  try {\n    const binary = use7z ? await getPath7za() : \"zip\"\n    await exec(\n      binary,\n      args,\n      {\n        cwd: options.withoutDir ? dirToArchive : path.dirname(dirToArchive),\n      },\n      debug7z.enabled\n    )\n  } catch (e: any) {\n    if (e.code === \"ENOENT\" && !(await exists(dirToArchive))) {\n      throw new Error(`Cannot create archive: \"${dirToArchive}\" doesn't exist`)\n    } else {\n      throw e\n    }\n  }\n\n  return outFile\n}\n\nfunction debug7zArgs(command: \"a\" | \"x\"): Array<string> {\n  const args = [command, \"-bd\"]\n  if (debug7z.enabled) {\n    args.push(\"-bb\")\n  }\n  return args\n}\n"]}