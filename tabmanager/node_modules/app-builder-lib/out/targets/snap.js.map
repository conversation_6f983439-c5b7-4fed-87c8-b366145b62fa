{"version": 3, "file": "snap.js", "sourceRoot": "", "sources": ["../../src/targets/snap.ts"], "names": [], "mappings": ";;AAAA,+CAAyK;AACzK,+DAAgE;AAChE,uCAA+C;AAC/C,qCAA8B;AAC9B,6BAA4B;AAC5B,iCAAgC;AAChC,kCAAyC;AAGzC,qDAAqD;AAErD,6CAAiD;AAGjD,MAAM,YAAY,GAAG,CAAC,SAAS,EAAE,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAA;AAE3K,MAAqB,UAAW,SAAQ,aAAM;IAK5C,YACE,IAAY,EACK,QAAuB,EACvB,MAAyB,EACjC,MAAc;QAEvB,KAAK,CAAC,IAAI,CAAC,CAAA;QAJM,aAAQ,GAAR,QAAQ,CAAe;QACvB,WAAM,GAAN,MAAM,CAAmB;QACjC,WAAM,GAAN,MAAM,CAAQ;QARhB,YAAO,GAAgB,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;QAEvH,qBAAgB,GAAG,KAAK,CAAA;IAS/B,CAAC;IAEO,cAAc,CAAC,MAAwC,EAAE,WAA0B;QACzF,MAAM,MAAM,GAAG,IAAA,6BAAe,EAAC,MAAM,EAAE,WAAW,CAAC,CAAA;QACnD,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAA;QAC/B,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,IAAU;QACvC,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,OAAO,CAAC,EAAE,CAAC;YACvD,IAAI,CAAC,IAAI,CAAC,mCAAmC,CAAC,cAAc,CAAC,EAAE,CAAC;gBAC9D,MAAM,IAAI,wCAAyB,CAAC,iDAAiD,CAAC,CAAA;YACxF,CAAC;YAED,kBAAG,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAA;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAA;QAC3D,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAE5D,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,YAAY,CAAC,CAAA;QAE7G,MAAM,KAAK,GAAG,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAE5D,MAAM,aAAa,GAAG,IAAA,8BAAO,EAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QACpD,MAAM,oBAAoB,GAAG,uBAAuB,EAAE,CAAA;QACtD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAA;QAEtF,IAAI,CAAC,gBAAgB;YACnB,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,KAAK;gBACrC,CAAC,IAAI,KAAK,mBAAI,CAAC,GAAG,IAAI,IAAI,KAAK,mBAAI,CAAC,MAAM,CAAC;gBAC3C,aAAa,CAAC,MAAM,KAAK,CAAC;gBAC1B,4BAA4B,CAAC,aAAa,EAAE,oBAAoB,CAAC,CAAA;QAEnE,MAAM,aAAa,GAAQ;YACzB,OAAO,EAAE,YAAY;YACrB,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,MAAM;SAChB,CAAA;QAED,MAAM,IAAI,GAAQ,IAAA,cAAI,EAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,6BAAe,EAAC,MAAM,CAAC,EAAE,gBAAgB,CAAC,EAAE,OAAO,CAAC,CAAC,CAAA;QACrG,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,OAAO,aAAa,CAAC,OAAO,CAAA;QAC9B,CAAC;QACD,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;YACxB,wCAAwC;YACxC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC7C,OAAO,aAAa,CAAC,OAAO,CAAA;YAC9B,CAAC;QACH,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAC5B,CAAC;QACD,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACxC,CAAC;QACD,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;YACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY,CAAA;QAC7C,CAAC;QACD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;QAC9B,CAAC;QACD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,aAAa,CAAC,KAAK,GAAG,MAAM,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;YACvD,KAAK,MAAM,QAAQ,IAAI,aAAa,CAAC,KAAK,EAAE,CAAC;gBAC3C,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACnC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;oBACxB,SAAQ;gBACV,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;oBAChB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;gBACjB,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAA;YACpC,CAAC;QACH,CAAC;QAED,IAAA,yBAAU,EAAC,IAAI,EAAE;YACf,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW;YAC3C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW;YAC/C,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC;YAChD,aAAa,EAAE,CAAC,IAAA,gCAAiB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAChD,IAAI,EAAE;gBACJ,CAAC,QAAQ,CAAC,EAAE,aAAa;aAC1B;YACD,KAAK,EAAE;gBACL,GAAG,EAAE;oBACH,gBAAgB,EAAE,aAAa;iBAChC;aACF;SACF,CAAC,CAAA;QAEF,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,aAAa,CAAC,SAAS,GAAG,GAAG,IAAI,CAAC,IAAI,UAAU,CAAA;QAClD,CAAC;QAED,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,aAAa,CAAC,KAAK,CAAA;YAC1B,OAAO,IAAI,CAAC,KAAK,CAAA;QACnB,CAAC;aAAM,CAAC;YACN,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAA;YAC3C,aAAa,CAAC,WAAW,GAAG;gBAC1B,eAAe,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;gBACtD,IAAI,EAAE,yDAAyD;gBAC/D,oBAAoB,EAAE,sBAAsB;gBAC5C,eAAe,EAAE;oBACf,oBAAoB;oBACpB,oCAAoC,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW;oBACpF,0CAA0C;oBAC1C,YAAY,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW;iBAC7D,CAAC,IAAI,CAAC,GAAG,CAAC;gBACX,GAAG,OAAO,CAAC,WAAW;aACvB,CAAA;YAED,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;oBACjC,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAA;oBACnC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;wBACxB,SAAQ;oBACV,CAAC;oBAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAA;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAG,aAAa,CAAA;QAClD,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QACtC,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,OAAO,GAAG,IAAA,8BAAO,EAAC,OAAO,CAAC,OAAO,CAAC,CAAA;QACzC,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,uDAAuD;QACvD,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,mCAAmC,EAAE,KAAK,CAAC,CAAA;QAC/H,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,MAAM;YAC7B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAE9C,MAAM,QAAQ,GAAG,MAAM,IAAA,+BAAkB,EAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,QAAQ,GAAG,IAAA,gCAAiB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAChD,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;QAE1J,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;QACvB,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC3B,IAAI,CAAC,IAAI,GAAG,mBAAmB,CAAA;YACjC,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAC9C,CAAC;QAED,yCAAyC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;QAChF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,UAAU,CAAC,CAAA;QACzE,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,GAAG,KAAK,EAAE,WAAW,EAAE;YAC9F,6CAA6C;YAC7C,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAA;QAEF,MAAM,YAAY,GAAkB,MAAA,OAAO,CAAC,cAAc,mCAAI,EAAE,CAAA;QAChE,IAAI,IAAI,CAAC,mCAAmC,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC;YACxF,MAAM,YAAY,GAAG,cAAc,CAAA;YACnC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;gBACzC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YACjC,CAAC;YACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;YAC1C,CAAC;QACH,CAAC;QACD,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;QACvD,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,oEAAoE;YACpE,MAAM,aAAa,GAAG,CAAC,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAA;YACzG,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC;QACH,CAAC;QAED,IAAI,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI,IAAI,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YACtJ,OAAM;QACR,CAAC;QAED,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,EAAE,IAAA,8BAAe,EAAC,IAAI,CAAC,CAAC,CAAA;QAEvH,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;QACxE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;QAChC,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,aAAa,QAAQ,EAAE,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,IAAA,gCAAiB,EAAC,IAAI,CAAC,CAAA;QAE7B,MAAM,aAAa,GAAG,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAEjE,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;YACrF,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;YACR,aAAa,EAAE,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,aAAa;SACjF,CAAC,CAAA;IACJ,CAAC;IAEO,mCAAmC,CAAC,OAAe;QACzD,OAAO,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,IAAI,OAAO,EAAE,OAAO,CAAC,CAAA;IAC7E,CAAC;CACF;AA5PD,6BA4PC;AAED,SAAS,qBAAqB,CAAC,MAAsB;;IACnD,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,MAAA,MAAM,CAAC,KAAK,0CAAE,OAAO,EAAE,CAAC;QAC1B,MAAM,eAAe,GAAG,kCAAkC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAEhF,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,eAAe,CAAA;QACxB,CAAC;IACH,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,MAAM,eAAe,GAAG,kCAAkC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAE1E,IAAI,eAAe,EAAE,CAAC;YACpB,OAAO,eAAe,CAAA;QACxB,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,kCAAkC,CAAC,iBAA0B;IACpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACrC,KAAK,MAAM,SAAS,IAAI,iBAAiB,EAAE,CAAC;YAC1C,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC;gBAClC,OAAO,SAAS,CAAA;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,OAAO,iBAAiB,KAAK,QAAQ,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACnF,OAAO,iBAAiB,CAAA;IAC1B,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,kBAAkB,CAAC,iBAA0B;IACpD,MAAM,yBAAyB,GAAG,iBAAqC,CAAA;IACvE,OAAO,CAAA,yBAAyB,aAAzB,yBAAyB,uBAAzB,yBAAyB,CAAE,QAAQ,MAAK,WAAW,CAAA;AAC5D,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAU;IACnC,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,mBAAI,CAAC,GAAG;YACX,OAAO,kBAAkB,CAAA;QAC3B,KAAK,mBAAI,CAAC,IAAI;YACZ,OAAO,gBAAgB,CAAA;QACzB,KAAK,mBAAI,CAAC,MAAM;YACd,uCAAuC;YACvC,OAAO,qBAAqB,CAAA;QAC9B,KAAK,mBAAI,CAAC,KAAK;YACb,OAAO,mBAAmB,CAAA;QAE5B;YACE,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;IAC/C,CAAC;AACH,CAAC;AAED,SAAS,4BAA4B,CAAC,CAAgB,EAAE,CAAgB;IACtE,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;IACb,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAA;IACb,CAAC,CAAC,IAAI,EAAE,CAAA;IACR,CAAC,CAAC,IAAI,EAAE,CAAA;IACR,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAA;AAC/E,CAAC;AAED,SAAS,0BAA0B,CAAC,GAAuE;IACzG,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,MAAM,GAAQ,EAAE,CAAA;IACtB,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;QACpD,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;QACrB,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,uBAAuB,CAAC,IAAS;IACxC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACvB,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,IAAI,CAAC,SAAS,KAAK,iBAAiB,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3E,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAS,uBAAuB;IAC9B,qFAAqF;IACrF,uCAAuC;IACvC,OAAO,CAAC,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,oBAAoB,EAAE,eAAe,CAAC,CAAA;AAClF,CAAC", "sourcesContent": ["import { Arch, deepAssign, executeApp<PERSON>uilder, InvalidConfigurationError, log, replaceDefault as _replaceDefault, serializeToYaml, toLinuxArchString } from \"builder-util\"\nimport { SnapStoreOptions, asArray } from \"builder-util-runtime\"\nimport { outputFile, readFile } from \"fs-extra\"\nimport { load } from \"js-yaml\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Publish, Target } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { PlugDescriptor, SnapOptions } from \"../options/SnapOptions\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { createStageDirPath } from \"./targetUtil\"\nimport { Configuration } from \"../configuration\"\n\nconst defaultPlugs = [\"desktop\", \"desktop-legacy\", \"home\", \"x11\", \"wayland\", \"unity7\", \"browser-support\", \"network\", \"gsettings\", \"audio-playback\", \"pulseaudio\", \"opengl\"]\n\nexport default class SnapTarget extends Target {\n  readonly options: SnapOptions = { ...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name] }\n\n  public isUseTemplateApp = false\n\n  constructor(\n    name: string,\n    private readonly packager: LinuxPackager,\n    private readonly helper: LinuxTargetHelper,\n    readonly outDir: string\n  ) {\n    super(name)\n  }\n\n  private replaceDefault(inList: Array<string> | null | undefined, defaultList: Array<string>) {\n    const result = _replaceDefault(inList, defaultList)\n    if (result !== defaultList) {\n      this.isUseTemplateApp = false\n    }\n    return result\n  }\n\n  private async createDescriptor(arch: Arch): Promise<any> {\n    if (!this.isElectronVersionGreaterOrEqualThan(\"4.0.0\")) {\n      if (!this.isElectronVersionGreaterOrEqualThan(\"2.0.0-beta.1\")) {\n        throw new InvalidConfigurationError(\"Electron 2 and higher is required to build Snap\")\n      }\n\n      log.warn(\"Electron 4 and higher is highly recommended for Snap\")\n    }\n\n    const appInfo = this.packager.appInfo\n    const snapName = this.packager.executableName.toLowerCase()\n    const options = this.options\n\n    const plugs = normalizePlugConfiguration(this.options.plugs)\n\n    const plugNames = this.replaceDefault(plugs == null ? null : Object.getOwnPropertyNames(plugs), defaultPlugs)\n\n    const slots = normalizePlugConfiguration(this.options.slots)\n\n    const buildPackages = asArray(options.buildPackages)\n    const defaultStagePackages = getDefaultStagePackages()\n    const stagePackages = this.replaceDefault(options.stagePackages, defaultStagePackages)\n\n    this.isUseTemplateApp =\n      this.options.useTemplateApp !== false &&\n      (arch === Arch.x64 || arch === Arch.armv7l) &&\n      buildPackages.length === 0 &&\n      isArrayEqualRegardlessOfSort(stagePackages, defaultStagePackages)\n\n    const appDescriptor: any = {\n      command: \"command.sh\",\n      plugs: plugNames,\n      adapter: \"none\",\n    }\n\n    const snap: any = load(await readFile(path.join(getTemplatePath(\"snap\"), \"snapcraft.yaml\"), \"utf-8\"))\n    if (this.isUseTemplateApp) {\n      delete appDescriptor.adapter\n    }\n    if (options.base != null) {\n      snap.base = options.base\n      // from core22 onwards adapter is legacy\n      if (Number(snap.base.split(\"core\")[1]) >= 22) {\n        delete appDescriptor.adapter\n      }\n    }\n    if (options.grade != null) {\n      snap.grade = options.grade\n    }\n    if (options.confinement != null) {\n      snap.confinement = options.confinement\n    }\n    if (options.appPartStage != null) {\n      snap.parts.app.stage = options.appPartStage\n    }\n    if (options.layout != null) {\n      snap.layout = options.layout\n    }\n    if (slots != null) {\n      appDescriptor.slots = Object.getOwnPropertyNames(slots)\n      for (const slotName of appDescriptor.slots) {\n        const slotOptions = slots[slotName]\n        if (slotOptions == null) {\n          continue\n        }\n        if (!snap.slots) {\n          snap.slots = {}\n        }\n        snap.slots[slotName] = slotOptions\n      }\n    }\n\n    deepAssign(snap, {\n      name: snapName,\n      version: appInfo.version,\n      title: options.title || appInfo.productName,\n      summary: options.summary || appInfo.productName,\n      compression: options.compression,\n      description: this.helper.getDescription(options),\n      architectures: [toLinuxArchString(arch, \"snap\")],\n      apps: {\n        [snapName]: appDescriptor,\n      },\n      parts: {\n        app: {\n          \"stage-packages\": stagePackages,\n        },\n      },\n    })\n\n    if (options.autoStart) {\n      appDescriptor.autostart = `${snap.name}.desktop`\n    }\n\n    if (options.confinement === \"classic\") {\n      delete appDescriptor.plugs\n      delete snap.plugs\n    } else {\n      const archTriplet = archNameToTriplet(arch)\n      appDescriptor.environment = {\n        DISABLE_WAYLAND: options.allowNativeWayland ? \"\" : \"1\",\n        PATH: \"$SNAP/usr/sbin:$SNAP/usr/bin:$SNAP/sbin:$SNAP/bin:$PATH\",\n        SNAP_DESKTOP_RUNTIME: \"$SNAP/gnome-platform\",\n        LD_LIBRARY_PATH: [\n          \"$SNAP_LIBRARY_PATH\",\n          \"$SNAP/lib:$SNAP/usr/lib:$SNAP/lib/\" + archTriplet + \":$SNAP/usr/lib/\" + archTriplet,\n          \"$LD_LIBRARY_PATH:$SNAP/lib:$SNAP/usr/lib\",\n          \"$SNAP/lib/\" + archTriplet + \":$SNAP/usr/lib/\" + archTriplet,\n        ].join(\":\"),\n        ...options.environment,\n      }\n\n      if (plugs != null) {\n        for (const plugName of plugNames) {\n          const plugOptions = plugs[plugName]\n          if (plugOptions == null) {\n            continue\n          }\n\n          snap.plugs[plugName] = plugOptions\n        }\n      }\n    }\n\n    if (buildPackages.length > 0) {\n      snap.parts.app[\"build-packages\"] = buildPackages\n    }\n    if (options.after != null) {\n      snap.parts.app.after = options.after\n    }\n\n    if (options.assumes != null) {\n      snap.assumes = asArray(options.assumes)\n    }\n\n    return snap\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const options = this.options\n    // tslint:disable-next-line:no-invalid-template-strings\n    const artifactName = packager.expandArtifactNamePattern(this.options, \"snap\", arch, \"${name}_${version}_${arch}.${ext}\", false)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: \"snap\",\n      file: artifactPath,\n      arch,\n    })\n\n    const snap = await this.createDescriptor(arch)\n\n    const stageDir = await createStageDirPath(this, packager, arch)\n    const snapArch = toLinuxArchString(arch, \"snap\")\n    const args = [\"snap\", \"--app\", appOutDir, \"--stage\", stageDir, \"--arch\", snapArch, \"--output\", artifactPath, \"--executable\", this.packager.executableName]\n\n    await this.helper.icons\n    if (this.helper.maxIconPath != null) {\n      if (!this.isUseTemplateApp) {\n        snap.icon = \"snap/gui/icon.png\"\n      }\n      args.push(\"--icon\", this.helper.maxIconPath)\n    }\n\n    // snapcraft.yaml inside a snap directory\n    const snapMetaDir = path.join(stageDir, this.isUseTemplateApp ? \"meta\" : \"snap\")\n    const desktopFile = path.join(snapMetaDir, \"gui\", `${snap.name}.desktop`)\n    await this.helper.writeDesktopEntry(this.options, packager.executableName + \" %U\", desktopFile, {\n      // tslint:disable:no-invalid-template-strings\n      Icon: \"${SNAP}/meta/gui/icon.png\",\n    })\n\n    const extraAppArgs: Array<string> = options.executableArgs ?? []\n    if (this.isElectronVersionGreaterOrEqualThan(\"5.0.0\") && !isBrowserSandboxAllowed(snap)) {\n      const noSandboxArg = \"--no-sandbox\"\n      if (!extraAppArgs.includes(noSandboxArg)) {\n        extraAppArgs.push(noSandboxArg)\n      }\n      if (this.isUseTemplateApp) {\n        args.push(\"--exclude\", \"chrome-sandbox\")\n      }\n    }\n    if (extraAppArgs.length > 0) {\n      args.push(\"--extraAppArgs=\" + extraAppArgs.join(\" \"))\n    }\n\n    if (snap.compression != null) {\n      args.push(\"--compression\", snap.compression)\n    }\n\n    if (this.isUseTemplateApp) {\n      // remove fields that are valid in snapcraft.yaml, but not snap.yaml\n      const fieldsToStrip = [\"compression\", \"contact\", \"donation\", \"issues\", \"parts\", \"source-code\", \"website\"]\n      for (const field of fieldsToStrip) {\n        delete snap[field]\n      }\n    }\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && (await packager.packagerOptions.effectiveOptionComputed({ snap, desktopFile, args }))) {\n      return\n    }\n\n    await outputFile(path.join(snapMetaDir, this.isUseTemplateApp ? \"snap.yaml\" : \"snapcraft.yaml\"), serializeToYaml(snap))\n\n    const hooksDir = await packager.getResource(options.hooks, \"snap-hooks\")\n    if (hooksDir != null) {\n      args.push(\"--hooks\", hooksDir)\n    }\n\n    if (this.isUseTemplateApp) {\n      args.push(\"--template-url\", `electron4:${snapArch}`)\n    }\n\n    await executeAppBuilder(args)\n\n    const publishConfig = findSnapPublishConfig(this.packager.config)\n\n    await packager.info.callArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"snap\", arch, false),\n      target: this,\n      arch,\n      packager,\n      publishConfig: publishConfig == null ? { provider: \"snapStore\" } : publishConfig,\n    })\n  }\n\n  private isElectronVersionGreaterOrEqualThan(version: string) {\n    return semver.gte(this.packager.config.electronVersion || \"7.0.0\", version)\n  }\n}\n\nfunction findSnapPublishConfig(config?: Configuration): SnapStoreOptions | null {\n  if (!config) {\n    return null\n  }\n\n  if (config.linux?.publish) {\n    const configCandidate = findSnapPublishConfigInPublishNode(config.linux.publish)\n\n    if (configCandidate) {\n      return configCandidate\n    }\n  }\n\n  if (config.publish) {\n    const configCandidate = findSnapPublishConfigInPublishNode(config.publish)\n\n    if (configCandidate) {\n      return configCandidate\n    }\n  }\n\n  return null\n}\n\nfunction findSnapPublishConfigInPublishNode(configPublishNode: Publish): SnapStoreOptions | null {\n  if (!configPublishNode) {\n    return null\n  }\n\n  if (Array.isArray(configPublishNode)) {\n    for (const configObj of configPublishNode) {\n      if (isSnapStoreOptions(configObj)) {\n        return configObj\n      }\n    }\n  }\n\n  if (typeof configPublishNode === `object` && isSnapStoreOptions(configPublishNode)) {\n    return configPublishNode\n  }\n\n  return null\n}\n\nfunction isSnapStoreOptions(configPublishNode: Publish): configPublishNode is SnapStoreOptions {\n  const snapStoreOptionsCandidate = configPublishNode as SnapStoreOptions\n  return snapStoreOptionsCandidate?.provider === `snapStore`\n}\n\nfunction archNameToTriplet(arch: Arch): string {\n  switch (arch) {\n    case Arch.x64:\n      return \"x86_64-linux-gnu\"\n    case Arch.ia32:\n      return \"i386-linux-gnu\"\n    case Arch.armv7l:\n      // noinspection SpellCheckingInspection\n      return \"arm-linux-gnueabihf\"\n    case Arch.arm64:\n      return \"aarch64-linux-gnu\"\n\n    default:\n      throw new Error(`Unsupported arch ${arch}`)\n  }\n}\n\nfunction isArrayEqualRegardlessOfSort(a: Array<string>, b: Array<string>) {\n  a = a.slice()\n  b = b.slice()\n  a.sort()\n  b.sort()\n  return a.length === b.length && a.every((value, index) => value === b[index])\n}\n\nfunction normalizePlugConfiguration(raw: Array<string | PlugDescriptor> | PlugDescriptor | null | undefined): { [key: string]: { [name: string]: any } | null } | null {\n  if (raw == null) {\n    return null\n  }\n\n  const result: any = {}\n  for (const item of Array.isArray(raw) ? raw : [raw]) {\n    if (typeof item === \"string\") {\n      result[item] = null\n    } else {\n      Object.assign(result, item)\n    }\n  }\n  return result\n}\n\nfunction isBrowserSandboxAllowed(snap: any): boolean {\n  if (snap.plugs != null) {\n    for (const plugName of Object.keys(snap.plugs)) {\n      const plug = snap.plugs[plugName]\n      if (plug.interface === \"browser-support\" && plug[\"allow-sandbox\"] === true) {\n        return true\n      }\n    }\n  }\n  return false\n}\n\nfunction getDefaultStagePackages() {\n  // libxss1 - was \"error while loading shared libraries: libXss.so.1\" on Xubuntu 16.04\n  // noinspection SpellCheckingInspection\n  return [\"libnspr4\", \"libnss3\", \"libxss1\", \"libappindicator3-1\", \"libsecret-1-0\"]\n}\n"]}