{"version": 3, "file": "cacheManager.js", "sourceRoot": "", "sources": ["../../src/util/cacheManager.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAwC;AACxC,4CAA8C;AAC9C,sDAA+D;AAE/D,uCAA8C;AAC9C,0CAA6C;AAC7C,6BAA4B;AAM5B,MAAa,iBAAiB;IAW5B,YACE,MAAc,EACG,cAAsB,EACvC,IAAU;QADO,mBAAc,GAAd,cAAc,CAAQ;QANzC,cAAS,GAA0B,IAAI,CAAA;QAE/B,cAAS,GAAkB,IAAI,CAAA;QAOrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAA;QACvD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;QACpD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;IAC5D,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc;QAC9B,IAAI,CAAC,SAAS,GAAG,MAAM,CAAA;QAEvB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAA,8BAAoB,EAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;QACzE,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAA;QACjF,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YACzB,kBAAG,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,kCAAkC,CAAC,CAAA;YAC/E,OAAO,KAAK,CAAA;QACd,CAAC;QAED,kBAAG,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,2BAA2B,CAAC,CAAA;QAChG,IAAI,CAAC;YACH,MAAM,IAAA,aAAQ,EAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAA;YAC1D,OAAO,IAAI,CAAA;QACb,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAChD,kBAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,+BAA+B,CAAC,CAAA;YAC/D,CAAC;iBAAM,CAAC;gBACN,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,+BAA+B,CAAC,CAAA;YACpE,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,EAAE,gBAAgB,EAAE,IAAI,CAAC,SAAS,EAAE,CAAA;QACvD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAA;QAClD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;YAC/C,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,IAAA,oBAAS,EAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,IAAA,aAAQ,EAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;QAC1H,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,yBAAyB,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;;AA9DH,8CA+DC;AA9DQ,yBAAO,GAAG,GAAG,AAAN,CAAM;AAgEf,KAAK,UAAU,MAAM,CAAC,IAAU,EAAE,KAAoB;IAC3D,+HAA+H;IAC/H,KAAK,MAAM,OAAO,IAAI,MAAM,sBAAe,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,mBAAQ,EAAC,EAAE,CAAC,CAAC,EAAE,CAAC;QAC3E,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;IACtC,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC9B,CAAC;AARD,wBAQC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, log } from \"builder-util\"\nimport { copyFile } from \"builder-util/out/fs\"\nimport { orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { Hash } from \"crypto\"\nimport { readJson, writeJson } from \"fs-extra\"\nimport { mkdir, readFile } from \"fs/promises\"\nimport * as path from \"path\"\n\nexport interface BuildCacheInfo {\n  executableDigest: string\n}\n\nexport class BuildCacheManager {\n  static VERSION = \"0\"\n\n  readonly cacheDir: string\n  readonly cacheInfoFile: string\n  readonly cacheFile: string\n\n  cacheInfo: BuildCacheInfo | null = null\n\n  private newDigest: string | null = null\n\n  constructor(\n    outDir: string,\n    private readonly executableFile: string,\n    arch: Arch\n  ) {\n    this.cacheDir = path.join(outDir, \".cache\", Arch[arch])\n    this.cacheFile = path.join(this.cacheDir, \"app.exe\")\n    this.cacheInfoFile = path.join(this.cacheDir, \"info.json\")\n  }\n\n  async copyIfValid(digest: string): Promise<boolean> {\n    this.newDigest = digest\n\n    this.cacheInfo = await orNullIfFileNotExist(readJson(this.cacheInfoFile))\n    const oldDigest = this.cacheInfo == null ? null : this.cacheInfo.executableDigest\n    if (oldDigest !== digest) {\n      log.debug({ oldDigest, newDigest: digest }, \"no valid cached executable found\")\n      return false\n    }\n\n    log.debug({ cacheFile: this.cacheFile, file: this.executableFile }, `copying cached executable`)\n    try {\n      await copyFile(this.cacheFile, this.executableFile, false)\n      return true\n    } catch (e: any) {\n      if (e.code === \"ENOENT\" || e.code === \"ENOTDIR\") {\n        log.debug({ error: e.code }, \"copy cached executable failed\")\n      } else {\n        log.warn({ error: e.stack || e }, `cannot copy cached executable`)\n      }\n    }\n    return false\n  }\n\n  async save() {\n    if (this.newDigest == null) {\n      throw new Error(\"call copyIfValid before\")\n    }\n\n    if (this.cacheInfo == null) {\n      this.cacheInfo = { executableDigest: this.newDigest }\n    } else {\n      this.cacheInfo.executableDigest = this.newDigest\n    }\n\n    try {\n      await mkdir(this.cacheDir, { recursive: true })\n      await Promise.all([writeJson(this.cacheInfoFile, this.cacheInfo), copyFile(this.executableFile, this.cacheFile, false)])\n    } catch (e: any) {\n      log.warn({ error: e.stack || e }, `cannot save build cache`)\n    }\n  }\n}\n\nexport async function digest(hash: Hash, files: Array<string>) {\n  // do not use pipe - better do bulk file read (https://github.com/yarnpkg/yarn/commit/7a63e0d23c46a4564bc06645caf8a59690f04d01)\n  for (const content of await BluebirdPromise.map(files, it => readFile(it))) {\n    hash.update(content)\n  }\n\n  hash.update(BuildCacheManager.VERSION)\n  return hash.digest(\"base64\")\n}\n"]}