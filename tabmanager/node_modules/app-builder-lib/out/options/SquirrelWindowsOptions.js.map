{"version": 3, "file": "SquirrelWindowsOptions.js", "sourceRoot": "", "sources": ["../../src/options/SquirrelWindowsOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\n * Squirrel.Windows options.\n */\nimport { TargetSpecificOptions } from \"../core\"\n\nexport interface SquirrelWindowsOptions extends TargetSpecificOptions {\n  /**\n   * A URL to an ICO file to use as the application icon (displayed in Control Panel > Programs and Features). Defaults to the Electron icon.\n   *\n   * Please note — [local icon file url is not accepted](https://github.com/atom/grunt-electron-installer/issues/73), must be https/http.\n   *\n   * If you don't plan to build windows installer, you can omit it.\n   * If your project repository is public on GitHub, it will be `https://github.com/${u}/${p}/blob/master/build/icon.ico?raw=true` by default.\n   */\n  readonly iconUrl?: string | null\n\n  /**\n   * The path to a .gif file to display during install. `build/install-spinner.gif` will be used if exists (it is a recommended way to set)\n   * (otherwise [default](https://github.com/electron/windows-installer/blob/master/resources/install-spinner.gif)).\n   */\n  readonly loadingGif?: string | null\n\n  /**\n   * Whether to create an MSI installer. Defaults to `false` (MSI is not created).\n   */\n  readonly msi?: boolean\n\n  /**\n   * A URL to your existing updates. Or `true` to automatically set to your GitHub repository. If given, these will be downloaded to create delta updates.\n   */\n  readonly remoteReleases?: string | boolean | null\n\n  /**\n   * Authentication token for remote updates\n   */\n  readonly remoteToken?: string | null\n\n  /**\n   * Use `appId` to identify package instead of `name`.\n   */\n  readonly useAppIdAsId?: boolean\n\n  /**\n   * https://github.com/electron-userland/electron-builder/issues/1743\n   * @private\n   */\n  readonly name?: string\n}\n"]}