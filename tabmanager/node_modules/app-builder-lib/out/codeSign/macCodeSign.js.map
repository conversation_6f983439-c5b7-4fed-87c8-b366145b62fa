{"version": 3, "file": "macCodeSign.js", "sourceRoot": "", "sources": ["../../src/codeSign/macCodeSign.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,gDAA+H;AAC/H,4CAA8D;AAC9D,8CAAqD;AACrD,mCAAgD;AAChD,0CAAoC;AACpC,uCAA+B;AAC/B,2BAAoC;AACpC,6BAA4B;AAC5B,yCAAuC;AACvC,yCAA+D;AAC/D,yCAA8C;AAC9C,iFAAmF;AAEtE,QAAA,wBAAwB,GAAG,CAAC,2BAA2B,EAAE,yBAAyB,EAAE,sCAAsC,EAAE,oCAAoC,CAAC,CAAA;AAe9K,SAAgB,aAAa,CAAC,WAAW,GAAG,IAAI;IAC9C,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClC,IAAI,WAAW,EAAE,CAAC;YAChB,UAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,yBAAyB,EAAE,EAAE,wCAAwC,CAAC,CAAA;QAC3F,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,MAAM,iBAAiB,GACrB,kKAAkK;QAClK,6LAA6L,CAAA;IAE/L,IAAI,IAAA,oBAAa,GAAE,EAAE,CAAC;QACpB,IAAI,IAAA,gBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,CAAC;YAChD,IAAI,WAAW,EAAE,CAAC;gBAChB,UAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,WAAW,EAAE,CAAC;gBAChB,oEAAoE;gBACpE,UAAG,CAAC,IAAI,CACN,wEAAwE,GAAG,+DAA+D,GAAG,KAAK,iBAAiB,EAAE,CACtK,CAAA;YACH,CAAC;YACD,OAAO,KAAK,CAAA;QACd,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AA5BD,sCA4BC;AAEM,KAAK,UAAU,WAAW,CAC/B,KAAc,EACd,gBAA4B,EAC5B,SAAoC,EACpC,YAAuC,EACvC,kBAA2B;IAE3B,MAAM,SAAS,GAAW,EAAE,CAAA;IAC5B,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QACtB,SAAS,CAAC,MAAM,GAAG,EAAE,CAAA;QACrB,IAAI,IAAA,uCAA+B,GAAE,EAAE,CAAC;YACtC,SAAS,CAAC,MAAM,IAAI,sBAAsB,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,aACnE,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,sIACf,EAAE,CAAA;QACJ,CAAC;QACD,SAAS,CAAC,MAAM,IAAI,2CAA2C,CAAA;QAC/D,IAAI,CAAC,IAAA,uCAA+B,GAAE,EAAE,CAAC;YACvC,SAAS,CAAC,2BAA2B,GAAG,KAAK,CAAA;QAC/C,CAAC;IACH,CAAC;SAAM,CAAC;QACN,SAAS,CAAC,MAAM,GAAG,kFAAkF,CAAA;QACrG,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAA;IAChC,CAAC;IAED,MAAM,IAAI,GAAG,CAAC,eAAe,CAAC,CAAA;IAC9B,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACzB,CAAC;IAED,IAAI,SAAS,IAAI,IAAI,IAAI,IAAA,uCAA+B,GAAE,EAAE,CAAC;QAC3D,SAAS,CAAC,aAAa,GAAG,CAAC,MAAM,IAAA,WAAI,EAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;aAC9D,IAAI,EAAE;aACN,KAAK,CAAC,IAAI,CAAC;aACX,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC;aACzF,IAAI,CAAC,IAAI,CAAC,CAAA;IACf,CAAC;IAED,IAAI,KAAK,IAAI,kBAAkB,EAAE,CAAC;QAChC,MAAM,IAAI,KAAK,CAAC,YAAM,CAAC,aAAa,CAAC,wCAAwC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;IAC/G,CAAC;SAAM,CAAC;QACN,UAAG,CAAC,IAAI,CAAC,SAAS,EAAE,wCAAwC,CAAC,CAAA;IAC/D,CAAC;AACH,CAAC;AA1CD,kCA0CC;AAED,sJAAsJ;AACtJ,+LAA+L;AAC/L,wDAAwD;AACxD,mEAAmE;AACnE,MAAM,wBAAwB,GAAG,IAAI,eAAI,CAAO,KAAK,IAAI,EAAE;IACzD,oDAAoD;IACpD,MAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAA;IACpC,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAA,uBAAW,EAAC,6BAA6B,CAAC,CAAC,CAAA;IACvF,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,sCAAsC,CAAC,CAAA;IAChF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAM;QACrC,iBAAiB,EAAE;QACnB,IAAA,aAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,qBAAqB,CAAC,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,iBAAM,EAAC,eAAe,EAAE,YAAY,CAAC,CAAC;KAC9I,CAAC,CAAA;IACF,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QACjC,MAAM,IAAA,WAAI,EAAC,mBAAmB,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;IACpG,CAAC;AACH,CAAC,CAAC,CAAA;AAEF,SAAS,iBAAiB;IACxB,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA;IAC9C,OAAO,IAAA,sBAAe,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAA,YAAO,GAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,kBAAkB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;AACjH,CAAC;AAED,SAAS,iBAAiB;IACxB,OAAO,IAAA,WAAI,EAAC,mBAAmB,EAAE,CAAC,gBAAgB,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAC3E,EAAE;SACC,KAAK,CAAC,IAAI,CAAC;SACX,GAAG,CAAC,EAAE,CAAC,EAAE;QACR,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;QACnB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACrC,CAAC,CAAC;SACD,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAC/B,CAAA;AACH,CAAC;AAWD,SAAgB,cAAc,CAAC,YAAoB,EAAE,SAAS,GAAG,IAAI;IACnE,OAAO,IAAA,WAAI,EAAC,mBAAmB,EAAE,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;QACnF,IAAI,SAAS,EAAE,CAAC;YACd,UAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,EAAE,wBAAwB,CAAC,CAAA;QACjF,CAAC;QACD,OAAO,IAAA,mBAAc,EAAC,YAAY,CAAC,CAAA;IACrC,CAAC,CAAC,CAAA;AACJ,CAAC;AAPD,wCAOC;AAEM,KAAK,UAAU,cAAc,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAyB;IACpI,sCAAsC;IACtC,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAClC,MAAM,wBAAwB,CAAC,KAAK,CAAA;IACtC,CAAC;IAED,oEAAoE;IACpE,oBAAoB;IACpB,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,IAAA,WAAM,GAAE,EAAE,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;IACtK,oCAAoC;IACpC,6DAA6D;IAC7D,MAAM,cAAc,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QAClD,WAAW;IACb,CAAC,CAAC,CAAA;IAEF,MAAM,SAAS,GAAG,CAAC,OAAO,CAAC,CAAA;IAC3B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IAC7C,MAAM,gBAAgB,GAAG,IAAA,oBAAW,EAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;IAC3D,MAAM,gBAAgB,GAAG;QACvB,CAAC,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,YAAY,CAAC;QACzD,CAAC,iBAAiB,EAAE,IAAI,EAAE,gBAAgB,EAAE,YAAY,CAAC;QACzD,CAAC,uBAAuB,EAAE,YAAY,CAAC;KACxC,CAAA;IAED,8EAA8E;IAC9E,oEAAoE;IACpE,MAAM,IAAI,GAAG,MAAM,iBAAiB,EAAE,CAAA;IACtC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;QACjC,gBAAgB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAA;IAC1F,CAAC;IAED,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,2LAA2L;QAC3L,sBAAe,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAA,4BAAiB,EAAC,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACxH,sBAAe,CAAC,SAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,WAAI,EAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;KACjF,CAAC,CAAA;IACF,OAAO,MAAM,WAAW,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAkB,CAAC,CAAA;AAChI,CAAC;AAzCD,wCAyCC;AAED,KAAK,UAAU,WAAW,CAAC,YAAoB,EAAE,KAAoB,EAAE,YAA2B;IAChG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QAChC,MAAM,IAAA,WAAI,EAAC,mBAAmB,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,uBAAuB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAA;QAEnJ,6HAA6H;QAC7H,2FAA2F;QAC3F,MAAM,IAAA,WAAI,EAAC,mBAAmB,EAAE,CAAC,wBAAwB,EAAE,IAAI,EAAE,oBAAoB,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAA;IAC7H,CAAC;IAED,OAAO;QACL,YAAY;KACb,CAAA;AACH,CAAC;AAED,eAAe;AACf,SAAgB,IAAI,CAAC,IAAY,EAAE,IAAY,EAAE,QAAgB;IAC/D,MAAM,IAAI,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACxD,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAA;IACnC,CAAC;IACD,OAAO,IAAA,WAAI,EAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;AACxC,CAAC;AAND,oBAMC;AAEU,QAAA,qBAAqB,GAAkC,IAAI,CAAA;AAEtE,KAAK,UAAU,kBAAkB,CAAC,QAAwB;IACxD,SAAS,WAAW,CAAC,IAAmB;QACtC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACrB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,MAAM,GAAG,6BAAqB,CAAA;IAClC,IAAI,MAAM,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACvC,mEAAmE;QACnE,mEAAmE;QACnE,MAAM,GAAG,OAAO,CAAC,GAAG,CAAgB;YAClC,IAAA,WAAI,EAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CACxE,EAAE;iBACC,IAAI,EAAE;iBACN,KAAK,CAAC,IAAI,CAAC;iBACX,MAAM,CAAC,EAAE,CAAC,EAAE;gBACX,KAAK,MAAM,MAAM,IAAI,gCAAwB,EAAE,CAAC;oBAC9C,IAAI,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBACxB,OAAO,IAAI,CAAA;oBACb,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAA;YACd,CAAC,CAAC,CACL;YACD,IAAA,WAAI,EAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACvH,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YACX,MAAM,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;iBAChB,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;iBACb,MAAM,CACL,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC,CACvK;gBACD,YAAY;iBACX,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;YACtD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAA;QACnC,CAAC,CAAC,CAAA;QAEF,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,6BAAqB,GAAG,MAAM,CAAA;QAChC,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,IAAc,EAAE,SAAyB,EAAE,QAAwB;IAC9F,mEAAmE;IACnE,sCAAsC;IACtC,MAAM,KAAK,GAAG,MAAM,kBAAkB,CAAC,QAAQ,CAAC,CAAA;IAChD,MAAM,UAAU,GAAG,GAAG,IAAI,GAAG,CAAA;IAC7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACnD,SAAQ;QACV,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,OAAO,aAAa,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAED,IAAI,IAAI,KAAK,0BAA0B,EAAE,CAAC;QACxC,6BAA6B;QAC7B,mEAAmE;QACnE,CAAC,EAAE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YAC5B,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnD,SAAQ;YACV,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACpC,SAAQ;YACV,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,gCAAwB,EAAE,CAAC;gBAC9C,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,SAAS,CAAC,CAAA;gBACZ,CAAC;YACH,CAAC;YAED,OAAO,aAAa,CAAC,IAAI,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AASD,SAAS,aAAa,CAAC,IAAY;IACjC,MAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IACzC,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAA;IACvE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe,GAAG,CAAC,CAAC,CAAA;IACnD,OAAO,IAAI,0BAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAClC,CAAC;AAED,SAAgB,YAAY,CAAC,QAAkB,EAAE,SAAyB,EAAE,QAAwB;IAClG,IAAI,QAAQ,GAAG,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAA;IAChD,IAAI,IAAA,sBAAe,EAAC,QAAQ,CAAC,EAAE,CAAC;QAC9B,IAAI,IAAA,uCAA+B,GAAE,EAAE,CAAC;YACtC,OAAO,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAA;QAChD,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC9B,CAAC;IACH,CAAC;SAAM,CAAC;QACN,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAA;QAC1B,KAAK,MAAM,MAAM,IAAI,gCAAwB,EAAE,CAAC;YAC9C,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;QAC/B,CAAC;QACD,OAAO,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAA;IACpD,CAAC;AACH,CAAC;AAfD,oCAeC;AAED,SAAS,WAAW,CAAC,IAAY,EAAE,MAAc;IAC/C,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,MAAM,IAAI,gCAAyB,CAAC,yBAAyB,MAAM,kFAAkF,CAAC,CAAA;IACxJ,CAAC;AACH,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { exec, InvalidConfigurationError, isEmptyOrSpaces, isEnvTrue, isPullRequest, log, TmpDir } from \"builder-util/out/util\"\nimport { copyFile, unlinkIfExists } from \"builder-util/out/fs\"\nimport { Fields, Logger } from \"builder-util/out/log\"\nimport { randomBytes, createHash } from \"crypto\"\nimport { rename } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir, tmpdir } from \"os\"\nimport * as path from \"path\"\nimport { getTempName } from \"temp-file\"\nimport { isAutoDiscoveryCodeSignIdentity } from \"../util/flags\"\nimport { importCertificate } from \"./codesign\"\nimport { Identity as _Identity } from \"@electron/osx-sign/dist/cjs/util-identities\"\n\nexport const appleCertificatePrefixes = [\"Developer ID Application:\", \"Developer ID Installer:\", \"3rd Party Mac Developer Application:\", \"3rd Party Mac Developer Installer:\"]\n\nexport type CertType =\n  | \"Developer ID Application\"\n  | \"Developer ID Installer\"\n  | \"3rd Party Mac Developer Application\"\n  | \"3rd Party Mac Developer Installer\"\n  | \"Mac Developer\"\n  | \"Apple Development\"\n  | \"Apple Distribution\"\n\nexport interface CodeSigningInfo {\n  keychainFile?: string | null\n}\n\nexport function isSignAllowed(isPrintWarn = true): boolean {\n  if (process.platform !== \"darwin\") {\n    if (isPrintWarn) {\n      log.warn({ reason: \"supported only on macOS\" }, \"skipped macOS application code signing\")\n    }\n    return false\n  }\n\n  const buildForPrWarning =\n    \"There are serious security concerns with CSC_FOR_PULL_REQUEST=true (see the  CircleCI documentation (https://circleci.com/docs/1.0/fork-pr-builds/) for details)\" +\n    \"\\nIf you have SSH keys, sensitive env vars or AWS credentials stored in your project settings and untrusted forks can make pull requests against your repo, then this option isn't for you.\"\n\n  if (isPullRequest()) {\n    if (isEnvTrue(process.env.CSC_FOR_PULL_REQUEST)) {\n      if (isPrintWarn) {\n        log.warn(buildForPrWarning)\n      }\n    } else {\n      if (isPrintWarn) {\n        // https://github.com/electron-userland/electron-builder/issues/1524\n        log.warn(\n          \"Current build is a part of pull request, code signing will be skipped.\" + \"\\nSet env CSC_FOR_PULL_REQUEST to true to force code signing.\" + `\\n${buildForPrWarning}`\n        )\n      }\n      return false\n    }\n  }\n  return true\n}\n\nexport async function reportError(\n  isMas: boolean,\n  certificateTypes: CertType[],\n  qualifier: string | null | undefined,\n  keychainFile: string | null | undefined,\n  isForceCodeSigning: boolean\n) {\n  const logFields: Fields = {}\n  if (qualifier == null) {\n    logFields.reason = \"\"\n    if (isAutoDiscoveryCodeSignIdentity()) {\n      logFields.reason += `cannot find valid \"${certificateTypes.join(\", \")}\" identity${\n        isMas ? \"\" : ` or custom non-Apple code signing certificate, it could cause some undefined behaviour, e.g. macOS localized description not visible`\n      }`\n    }\n    logFields.reason += \", see https://electron.build/code-signing\"\n    if (!isAutoDiscoveryCodeSignIdentity()) {\n      logFields.CSC_IDENTITY_AUTO_DISCOVERY = false\n    }\n  } else {\n    logFields.reason = \"Identity name is specified, but no valid identity with this name in the keychain\"\n    logFields.identity = qualifier\n  }\n\n  const args = [\"find-identity\"]\n  if (keychainFile != null) {\n    args.push(keychainFile)\n  }\n\n  if (qualifier != null || isAutoDiscoveryCodeSignIdentity()) {\n    logFields.allIdentities = (await exec(\"/usr/bin/security\", args))\n      .trim()\n      .split(\"\\n\")\n      .filter(it => !(it.includes(\"Policy: X.509 Basic\") || it.includes(\"Matching identities\")))\n      .join(\"\\n\")\n  }\n\n  if (isMas || isForceCodeSigning) {\n    throw new Error(Logger.createMessage(\"skipped macOS application code signing\", logFields, \"error\", it => it))\n  } else {\n    log.warn(logFields, \"skipped macOS application code signing\")\n  }\n}\n\n// \"Note that filename will not be searched to resolve the signing identity's certificate chain unless it is also on the user's keychain search list.\"\n// but \"security list-keychains\" doesn't support add - we should 1) get current list 2) set new list - it is very bad http://stackoverflow.com/questions/10538942/add-a-keychain-to-search-list\n// \"overly complicated and introduces a race condition.\"\n// https://github.com/electron-userland/electron-builder/issues/398\nconst bundledCertKeychainAdded = new Lazy<void>(async () => {\n  // copy to temp and then atomic rename to final path\n  const cacheDir = getCacheDirectory()\n  const tmpKeychainPath = path.join(cacheDir, getTempName(\"electron-builder-root-certs\"))\n  const keychainPath = path.join(cacheDir, \"electron-builder-root-certs.keychain\")\n  const results = await Promise.all<any>([\n    listUserKeychains(),\n    copyFile(path.join(__dirname, \"..\", \"..\", \"certs\", \"root_certs.keychain\"), tmpKeychainPath).then(() => rename(tmpKeychainPath, keychainPath)),\n  ])\n  const list = results[0]\n  if (!list.includes(keychainPath)) {\n    await exec(\"/usr/bin/security\", [\"list-keychains\", \"-d\", \"user\", \"-s\", keychainPath].concat(list))\n  }\n})\n\nfunction getCacheDirectory(): string {\n  const env = process.env.ELECTRON_BUILDER_CACHE\n  return isEmptyOrSpaces(env) ? path.join(homedir(), \"Library\", \"Caches\", \"electron-builder\") : path.resolve(env)\n}\n\nfunction listUserKeychains(): Promise<Array<string>> {\n  return exec(\"/usr/bin/security\", [\"list-keychains\", \"-d\", \"user\"]).then(it =>\n    it\n      .split(\"\\n\")\n      .map(it => {\n        const r = it.trim()\n        return r.substring(1, r.length - 1)\n      })\n      .filter(it => it.length > 0)\n  )\n}\n\nexport interface CreateKeychainOptions {\n  tmpDir: TmpDir\n  cscLink: string\n  cscKeyPassword: string\n  cscILink?: string | null\n  cscIKeyPassword?: string | null\n  currentDir: string\n}\n\nexport function removeKeychain(keychainFile: string, printWarn = true): Promise<any> {\n  return exec(\"/usr/bin/security\", [\"delete-keychain\", keychainFile]).catch((e: any) => {\n    if (printWarn) {\n      log.warn({ file: keychainFile, error: e.stack || e }, \"cannot delete keychain\")\n    }\n    return unlinkIfExists(keychainFile)\n  })\n}\n\nexport async function createKeychain({ tmpDir, cscLink, cscKeyPassword, cscILink, cscIKeyPassword, currentDir }: CreateKeychainOptions): Promise<CodeSigningInfo> {\n  // travis has correct AppleWWDRCA cert\n  if (process.env.TRAVIS !== \"true\") {\n    await bundledCertKeychainAdded.value\n  }\n\n  // https://github.com/electron-userland/electron-builder/issues/3685\n  // use constant file\n  const keychainFile = path.join(process.env.APP_BUILDER_TMP_DIR || tmpdir(), `${createHash(\"sha256\").update(currentDir).update(\"app-builder\").digest(\"hex\")}.keychain`)\n  // noinspection JSUnusedLocalSymbols\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  await removeKeychain(keychainFile, false).catch(_ => {\n    /* ignore*/\n  })\n\n  const certLinks = [cscLink]\n  if (cscILink != null) {\n    certLinks.push(cscILink)\n  }\n\n  const certPaths = new Array(certLinks.length)\n  const keychainPassword = randomBytes(32).toString(\"base64\")\n  const securityCommands = [\n    [\"create-keychain\", \"-p\", keychainPassword, keychainFile],\n    [\"unlock-keychain\", \"-p\", keychainPassword, keychainFile],\n    [\"set-keychain-settings\", keychainFile],\n  ]\n\n  // https://stackoverflow.com/questions/42484678/codesign-keychain-gets-ignored\n  // https://github.com/electron-userland/electron-builder/issues/1457\n  const list = await listUserKeychains()\n  if (!list.includes(keychainFile)) {\n    securityCommands.push([\"list-keychains\", \"-d\", \"user\", \"-s\", keychainFile].concat(list))\n  }\n\n  await Promise.all([\n    // we do not clear downloaded files - will be removed on tmpDir cleanup automatically. not a security issue since in any case data is available as env variables and protected by password.\n    BluebirdPromise.map(certLinks, (link, i) => importCertificate(link, tmpDir, currentDir).then(it => (certPaths[i] = it))),\n    BluebirdPromise.mapSeries(securityCommands, it => exec(\"/usr/bin/security\", it)),\n  ])\n  return await importCerts(keychainFile, certPaths, [cscKeyPassword, cscIKeyPassword].filter(it => it != null) as Array<string>)\n}\n\nasync function importCerts(keychainFile: string, paths: Array<string>, keyPasswords: Array<string>): Promise<CodeSigningInfo> {\n  for (let i = 0; i < paths.length; i++) {\n    const password = keyPasswords[i]\n    await exec(\"/usr/bin/security\", [\"import\", paths[i], \"-k\", keychainFile, \"-T\", \"/usr/bin/codesign\", \"-T\", \"/usr/bin/productbuild\", \"-P\", password])\n\n    // https://stackoverflow.com/questions/39868578/security-codesign-in-sierra-keychain-ignores-access-control-settings-and-ui-p\n    // https://github.com/electron-userland/electron-packager/issues/701#issuecomment-322315996\n    await exec(\"/usr/bin/security\", [\"set-key-partition-list\", \"-S\", \"apple-tool:,apple:\", \"-s\", \"-k\", password, keychainFile])\n  }\n\n  return {\n    keychainFile,\n  }\n}\n\n/** @private */\nexport function sign(path: string, name: string, keychain: string): Promise<any> {\n  const args = [\"--deep\", \"--force\", \"--sign\", name, path]\n  if (keychain != null) {\n    args.push(\"--keychain\", keychain)\n  }\n  return exec(\"/usr/bin/codesign\", args)\n}\n\nexport let findIdentityRawResult: Promise<Array<string>> | null = null\n\nasync function getValidIdentities(keychain?: string | null): Promise<Array<string>> {\n  function addKeychain(args: Array<string>) {\n    if (keychain != null) {\n      args.push(keychain)\n    }\n    return args\n  }\n\n  let result = findIdentityRawResult\n  if (result == null || keychain != null) {\n    // https://github.com/electron-userland/electron-builder/issues/481\n    // https://github.com/electron-userland/electron-builder/issues/535\n    result = Promise.all<Array<string>>([\n      exec(\"/usr/bin/security\", addKeychain([\"find-identity\", \"-v\"])).then(it =>\n        it\n          .trim()\n          .split(\"\\n\")\n          .filter(it => {\n            for (const prefix of appleCertificatePrefixes) {\n              if (it.includes(prefix)) {\n                return true\n              }\n            }\n            return false\n          })\n      ),\n      exec(\"/usr/bin/security\", addKeychain([\"find-identity\", \"-v\", \"-p\", \"codesigning\"])).then(it => it.trim().split(\"\\n\")),\n    ]).then(it => {\n      const array = it[0]\n        .concat(it[1])\n        .filter(\n          it => !it.includes(\"(Missing required extension)\") && !it.includes(\"valid identities found\") && !it.includes(\"iPhone \") && !it.includes(\"com.apple.idms.appleid.prd.\")\n        )\n        // remove 1)\n        .map(it => it.substring(it.indexOf(\")\") + 1).trim())\n      return Array.from(new Set(array))\n    })\n\n    if (keychain == null) {\n      findIdentityRawResult = result\n    }\n  }\n  return result\n}\n\nasync function _findIdentity(type: CertType, qualifier?: string | null, keychain?: string | null): Promise<Identity | null> {\n  // https://github.com/electron-userland/electron-builder/issues/484\n  //noinspection SpellCheckingInspection\n  const lines = await getValidIdentities(keychain)\n  const namePrefix = `${type}:`\n  for (const line of lines) {\n    if (qualifier != null && !line.includes(qualifier)) {\n      continue\n    }\n\n    if (line.includes(namePrefix)) {\n      return parseIdentity(line)\n    }\n  }\n\n  if (type === \"Developer ID Application\") {\n    // find non-Apple certificate\n    // https://github.com/electron-userland/electron-builder/issues/458\n    l: for (const line of lines) {\n      if (qualifier != null && !line.includes(qualifier)) {\n        continue\n      }\n\n      if (line.includes(\"Mac Developer:\")) {\n        continue\n      }\n\n      for (const prefix of appleCertificatePrefixes) {\n        if (line.includes(prefix)) {\n          continue l\n        }\n      }\n\n      return parseIdentity(line)\n    }\n  }\n  return null\n}\n\nexport declare class Identity {\n  readonly name: string\n  readonly hash?: string\n\n  constructor(name: string, hash?: string)\n}\n\nfunction parseIdentity(line: string): Identity {\n  const firstQuoteIndex = line.indexOf('\"')\n  const name = line.substring(firstQuoteIndex + 1, line.lastIndexOf('\"'))\n  const hash = line.substring(0, firstQuoteIndex - 1)\n  return new _Identity(name, hash)\n}\n\nexport function findIdentity(certType: CertType, qualifier?: string | null, keychain?: string | null): Promise<Identity | null> {\n  let identity = qualifier || process.env.CSC_NAME\n  if (isEmptyOrSpaces(identity)) {\n    if (isAutoDiscoveryCodeSignIdentity()) {\n      return _findIdentity(certType, null, keychain)\n    } else {\n      return Promise.resolve(null)\n    }\n  } else {\n    identity = identity.trim()\n    for (const prefix of appleCertificatePrefixes) {\n      checkPrefix(identity, prefix)\n    }\n    return _findIdentity(certType, identity, keychain)\n  }\n}\n\nfunction checkPrefix(name: string, prefix: string) {\n  if (name.startsWith(prefix)) {\n    throw new InvalidConfigurationError(`Please remove prefix \"${prefix}\" from the specified name — appropriate certificate will be chosen automatically`)\n  }\n}\n"]}