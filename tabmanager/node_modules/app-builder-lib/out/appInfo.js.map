{"version": 3, "file": "appInfo.js", "sourceRoot": "", "sources": ["../src/appInfo.ts"], "names": [], "mappings": ";;;AAAA,+CAAmD;AACnD,mCAAmC;AAGnC,wDAAkD;AAClD,8CAAkD;AAElD,oGAAoG;AACpG,mEAAmE;AACnE,SAAgB,OAAO,CAAC,CAAS;IAC/B,kBAAkB;IAClB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAA;IACjD,gCAAgC;IAChC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IAC7B,kBAAkB;IAClB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC,CAAA;IACvD,kBAAkB;IAClB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IAC7B,OAAO,CAAC,CAAA;AACV,CAAC;AAVD,0BAUC;AAED,MAAa,OAAO;IAclB,YACmB,IAAc,EAC/B,YAAuC,EACtB,0BAA+D,IAAI,EACpF,YAAY,GAAG,KAAK;;QAHH,SAAI,GAAJ,IAAI,CAAU;QAEd,4BAAuB,GAAvB,uBAAuB,CAA4C;QAhB7E,gBAAW,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,EAAE,CAAC,CAAA;QAmBlE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAQ,CAAA;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAA;QAE9B,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;QACzC,CAAC;QAED,MAAM,eAAe,GACnB,OAAO,CAAC,GAAG,CAAC,YAAY;YACxB,OAAO,CAAC,GAAG,CAAC,mBAAmB;YAC/B,OAAO,CAAC,GAAG,CAAC,qBAAqB;YACjC,OAAO,CAAC,GAAG,CAAC,gBAAgB;YAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB;YAC7B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAA;QAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,eAAe,CAAA;QAC7D,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,YAAY,GAAG,IAAI,CAAC,OAAO,CAAA;YAC3B,IAAI,CAAC,IAAA,8BAAe,EAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvC,YAAY,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,CAAA;YACxC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAEhC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAA;QAChD,CAAC;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE,CAAC;YACtC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,QAAQ,CAAC,mBAAmB,CAAA;QAC9D,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAK,CAAA;QAC9F,IAAI,CAAC,oBAAoB,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAAA;QAE5E,MAAM,cAAc,GAAG,MAAA,uBAAuB,aAAvB,uBAAuB,uBAAvB,uBAAuB,CAAE,cAAc,mCAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAA;QAC5F,IAAI,CAAC,eAAe,GAAG,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,IAAA,2BAAgB,EAAC,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAA;IAC5H,CAAC;IAED,IAAI,OAAO;QACT,MAAM,cAAc,GAAG,IAAA,mBAAU,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAC/C,IAAI,cAAc,IAAI,IAAI,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,OAAO,cAAc,CAAC,CAAC,CAAkB,CAAA;QAC3C,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,4BAA4B,CAAC,gBAAgB,GAAG,IAAI;QAClD,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAA;QAC3G,qFAAqF;QACrF,gCAAgC;QAChC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QAC7D,CAAC;QACD,mFAAmF;QACnF,MAAM,KAAK,GAAG,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,CAAC,CAAA;QAC9B,MAAM,KAAK,GAAG,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,CAAC,CAAA;QAC9B,sEAAsE;QACtE,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAA;QACtE,CAAC;QACD,2FAA2F;QAC3F,IAAI,WAAW,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAA;QAC5D,IAAI,WAAW,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;YACtD,WAAW,GAAG,GAAG,CAAA;QACnB,CAAC;QACD,OAAO,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,CAAA;IACpD,CAAC;IAED,IAAY,kBAAkB;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,EAAE,CAAA;IACpC,CAAC;IAED,IAAI,WAAW;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAA;QAC1E,OAAO,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAA;IAC5C,CAAC;IAED,IAAI,EAAE;QACJ,IAAI,KAAK,GAA8B,IAAI,CAAA;QAC3C,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACvE,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBACrC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;YACvB,CAAC;QACH,CAAC;QAED,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YACtB,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAK,CAAC,WAAW,EAAE,EAAE,CAAA;QACnF,CAAC,CAAA;QAED,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,IAAI,IAAA,8BAAe,EAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACrE,MAAM,cAAc,GAAG,KAAK,CAAA;YAC5B,KAAK,GAAG,oBAAoB,EAAE,CAAA;YAC9B,kBAAG,CAAC,IAAI,CAAC,eAAe,cAAc,gBAAgB,KAAK,wBAAwB,CAAC,CAAA;QACtF,CAAC;QAED,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;IACvD,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IAC1C,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,CAAA;IACjC,CAAC;IAED,IAAI,gBAAgB;QAClB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACtB,oEAAoE;QACpE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAA;IAChE,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAA,2BAAgB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IACpC,CAAC;IAED,IAAI,mBAAmB;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,GAAG,UAAU,CAAA;IACtD,CAAC;IAED,IAAI,SAAS;QACX,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAA;QAC5C,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,OAAO,IAAA,2BAAW,EAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QAC3C,CAAC;QACD,OAAO,eAAe,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,CAAA;IAC1F,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAA;QAC3E,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,OAAO,GAAG,CAAA;QACZ,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAA;QAC3C,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAA;IAC9G,CAAC;CACF;AA7JD,0BA6JC;AAED,gBAAgB;AAChB,SAAgB,wBAAwB,CAAC,UAAkB;IACzD,kGAAkG;IAClG,0LAA0L;IAC1L,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;AACrE,CAAC;AAJD,4DAIC", "sourcesContent": ["import { isEmptyOrSpaces, log } from \"builder-util\"\nimport { prerelease } from \"semver\"\nimport { PlatformSpecificBuildOptions } from \"./options/PlatformSpecificBuildOptions\"\nimport { Packager } from \"./packager\"\nimport { expandMacro } from \"./util/macroExpander\"\nimport { sanitizeFileName } from \"./util/filename\"\n\n// fpm bug - rpm build --description is not escaped, well... decided to replace quite to smart quote\n// http://leancrew.com/all-this/2010/11/smart-quotes-in-javascript/\nexport function smarten(s: string): string {\n  // opening singles\n  s = s.replace(/(^|[-\\u2014\\s([\"])'/g, \"$1\\u2018\")\n  // closing singles & apostrophes\n  s = s.replace(/'/g, \"\\u2019\")\n  // opening doubles\n  s = s.replace(/(^|[-\\u2014/[(\\u2018\\s])\"/g, \"$1\\u201c\")\n  // closing doubles\n  s = s.replace(/\"/g, \"\\u201d\")\n  return s\n}\n\nexport class AppInfo {\n  readonly description = smarten(this.info.metadata.description || \"\")\n  readonly version: string\n  readonly type: string | undefined\n  readonly shortVersion: string | undefined\n  readonly shortVersionWindows: string | undefined\n\n  readonly buildNumber: string | undefined\n  readonly buildVersion: string\n\n  readonly productName: string\n  readonly sanitizedProductName: string\n  readonly productFilename: string\n\n  constructor(\n    private readonly info: Packager,\n    buildVersion: string | null | undefined,\n    private readonly platformSpecificOptions: PlatformSpecificBuildOptions | null = null,\n    normalizeNfd = false\n  ) {\n    this.version = info.metadata.version!\n    this.type = info.metadata.type\n\n    if (buildVersion == null) {\n      buildVersion = info.config.buildVersion\n    }\n\n    const buildNumberEnvs =\n      process.env.BUILD_NUMBER ||\n      process.env.TRAVIS_BUILD_NUMBER ||\n      process.env.APPVEYOR_BUILD_NUMBER ||\n      process.env.CIRCLE_BUILD_NUM ||\n      process.env.BUILD_BUILDNUMBER ||\n      process.env.CI_PIPELINE_IID\n    this.buildNumber = info.config.buildNumber || buildNumberEnvs\n    if (buildVersion == null) {\n      buildVersion = this.version\n      if (!isEmptyOrSpaces(this.buildNumber)) {\n        buildVersion += `.${this.buildNumber}`\n      }\n    }\n    this.buildVersion = buildVersion\n\n    if (info.metadata.shortVersion) {\n      this.shortVersion = info.metadata.shortVersion\n    }\n    if (info.metadata.shortVersionWindows) {\n      this.shortVersionWindows = info.metadata.shortVersionWindows\n    }\n\n    this.productName = info.config.productName || info.metadata.productName || info.metadata.name!\n    this.sanitizedProductName = sanitizeFileName(this.productName, normalizeNfd)\n\n    const executableName = platformSpecificOptions?.executableName ?? info.config.executableName\n    this.productFilename = executableName != null ? sanitizeFileName(executableName, normalizeNfd) : this.sanitizedProductName\n  }\n\n  get channel(): string | null {\n    const prereleaseInfo = prerelease(this.version)\n    if (prereleaseInfo != null && prereleaseInfo.length > 0) {\n      return prereleaseInfo[0] as string | null\n    }\n    return null\n  }\n\n  getVersionInWeirdWindowsForm(isSetBuildNumber = true): string {\n    const [major, maybe_minor, maybe_patch] = this.version.split(\".\").map(versionPart => parseInt(versionPart))\n    // The major component must be present. Here it can be either NaN or undefined, which\n    // both returns true from isNaN.\n    if (isNaN(major)) {\n      throw new Error(`Invalid major number in: ${this.version}`)\n    }\n    // Allow missing version parts. Minor and patch can be left out and default to zero\n    const minor = maybe_minor ?? 0\n    const patch = maybe_patch ?? 0\n    // ... but reject non-integer version parts. '1.a' is not going to fly\n    if (isNaN(minor) || isNaN(patch)) {\n      throw new Error(`Invalid minor or patch number in: ${this.version}`)\n    }\n    // https://github.com/electron-userland/electron-builder/issues/2635#issuecomment-*********\n    let buildNumber = isSetBuildNumber ? this.buildNumber : null\n    if (buildNumber == null || !/^\\d+$/.test(buildNumber)) {\n      buildNumber = \"0\"\n    }\n    return `${major}.${minor}.${patch}.${buildNumber}`\n  }\n\n  private get notNullDevMetadata() {\n    return this.info.devMetadata || {}\n  }\n\n  get companyName(): string | null {\n    const author = this.info.metadata.author || this.notNullDevMetadata.author\n    return author == null ? null : author.name\n  }\n\n  get id(): string {\n    let appId: string | null | undefined = null\n    for (const options of [this.platformSpecificOptions, this.info.config]) {\n      if (options != null && appId == null) {\n        appId = options.appId\n      }\n    }\n\n    const generateDefaultAppId = () => {\n      const info = this.info\n      return `${info.framework.defaultAppIdPrefix}${info.metadata.name!.toLowerCase()}`\n    }\n\n    if (appId != null && (appId === \"your.id\" || isEmptyOrSpaces(appId))) {\n      const incorrectAppId = appId\n      appId = generateDefaultAppId()\n      log.warn(`do not use \"${incorrectAppId}\" as appId, \"${appId}\" will be used instead`)\n    }\n\n    return appId == null ? generateDefaultAppId() : appId\n  }\n\n  get macBundleIdentifier(): string {\n    return filterCFBundleIdentifier(this.id)\n  }\n\n  get name(): string {\n    return this.info.metadata.name!\n  }\n\n  get linuxPackageName(): string {\n    const name = this.name\n    // https://github.com/electron-userland/electron-builder/issues/2963\n    return name.startsWith(\"@\") ? this.sanitizedProductName : name\n  }\n\n  get sanitizedName(): string {\n    return sanitizeFileName(this.name)\n  }\n\n  get updaterCacheDirName(): string {\n    return this.sanitizedName.toLowerCase() + \"-updater\"\n  }\n\n  get copyright(): string {\n    const copyright = this.info.config.copyright\n    if (copyright != null) {\n      return expandMacro(copyright, null, this)\n    }\n    return `Copyright © ${new Date().getFullYear()} ${this.companyName || this.productName}`\n  }\n\n  async computePackageUrl(): Promise<string | null> {\n    const url = this.info.metadata.homepage || this.notNullDevMetadata.homepage\n    if (url != null) {\n      return url\n    }\n\n    const info = await this.info.repositoryInfo\n    return info == null || info.type !== \"github\" ? null : `https://${info.domain}/${info.user}/${info.project}`\n  }\n}\n\n/** @internal */\nexport function filterCFBundleIdentifier(identifier: string) {\n  // Remove special characters and allow only alphanumeric (A-Z,a-z,0-9), hyphen (-), and period (.)\n  // Apple documentation: https://developer.apple.com/library/mac/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-102070\n  return identifier.replace(/ /g, \"-\").replace(/[^a-zA-Z0-9.-]/g, \"\")\n}\n"]}