from .alias import *
from .bookmark import *

__all__ = [ 'ALIAS_KIND_FILE', 'ALIAS_KIND_FOLDER',
            'ALIAS_HFS_VOLUME_SIGNATURE',
            'ALIAS_FIXED_DISK', 'ALIAS_NETWORK_DISK', 'ALIAS_400KB_FLOPPY_DISK',
            'ALIAS_800KB_FLOPPY_DISK', 'ALIAS_1_44MB_FLOPPY_DISK',
            'ALIAS_EJECTABLE_DISK',
            'ALIAS_NO_CNID',
            'kBookmarkPath', 'kBookmarkCNIDPath', 'kBookmarkFileProperties',
            'kBookmarkFileName', 'kBookmarkFileID', 'kBookmarkFileCreationDate',
            'kBookmarkTOCPath', 'kBookmarkVolumePath',
            'kBookmarkVolumeURL', 'kBookmarkVolumeName', 'kBookmarkVolumeUUID',
            'kBookmarkVolumeSize', 'kBookmarkVolumeCreationDate',
            'kBookmarkVolumeProperties', 'kBookmarkContainingFolder',
            'kBookmarkUserName', 'kBookmarkUID', 'kBookmarkWasFileReference',
            'kBookmarkCreationOptions', 'kBookmarkURLLengths',
            'kBookmarkSecurityExtension',
            'AppleShareInfo',
            'VolumeInfo',
            'TargetInfo',
            'Alias',
            'Bookmark',
            'Data',
            'URL' ]


