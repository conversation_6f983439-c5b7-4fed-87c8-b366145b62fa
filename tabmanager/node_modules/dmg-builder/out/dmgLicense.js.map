{"version": 3, "file": "dmgLicense.js", "sourceRoot": "", "sources": ["../src/dmgLicense.ts"], "names": [], "mappings": ";;;AAAA,+CAAkC;AAClC,qCAA8B;AAE9B,8DAAkE;AAClE,uCAA6C;AAC7C,qDAAwD;AACxD,6CAAgD;AAUzC,KAAK,UAAU,eAAe,CAAC,QAA+B,EAAE,OAAe;IACpF,MAAM,YAAY,GAAG,MAAM,IAAA,yBAAe,EAAC,QAAQ,CAAC,CAAA;IACpD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,kBAAkB,GAAG,MAAM,IAAA,sCAAqB,EAAC,QAAQ,CAAC,CAAA;IAChE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAA;IAC1D,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,oBAAoB,EAAE,kBAAkB,CAAC,CAAA;IAElE,MAAM,QAAQ,GAAkB;QAC9B,OAAO,EAAE,sEAAsE;QAC/E,mBAAmB;QACnB,IAAI,EAAE,EAAE;QACR,MAAM,EAAE,EAAE;KACX,CAAA;IAED,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;QAChC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;SAC5C,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,MAAM,MAAM,IAAI,kBAAkB,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAA;QAC5B,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAA,cAAI,EAAC,MAAM,IAAA,mBAAQ,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAA,mBAAQ,EAAC,QAAQ,CAAC,CAAA;QAC5G,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,WAAW,CAAA;YACjC,OAAO,KAAK,CAAC,WAAW,CAAA;QAC1B,CAAC;QACD,QAAQ,CAAC,MAAM,CAAC,IAAI,CAClB,MAAM,CAAC,MAAM,CACX;YACE,IAAI,EAAE,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;SAC9C,EACD,KAAK,CACN,CACF,CAAA;IACH,CAAC;IAED,MAAM,IAAA,gCAAkB,EAAC,OAAO,EAAE,QAAQ,EAAE;QAC1C,eAAe,EAAE,kBAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAG,CAAC;KACpC,CAAC,CAAA;IAEF,OAAO,QAAQ,CAAA;AACjB,CAAC;AA/CD,0CA+CC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { load } from \"js-yaml\"\nimport { PlatformPackager } from \"app-builder-lib\"\nimport { getLicenseFiles } from \"app-builder-lib/out/util/license\"\nimport { readFile, readJson } from \"fs-extra\"\nimport { getLicenseButtonsFile } from \"./licenseButtons\"\nimport { dmgLicenseFromJSON } from \"dmg-license\"\n\n// License Specifications\n// https://github.com/argv-minus-one/dmg-license/blob/HEAD/docs/License%20Specifications.md\ntype LicenseConfig = {\n  $schema: string\n  body: any[]\n  labels: any[]\n}\n\nexport async function addLicenseToDmg(packager: PlatformPackager<any>, dmgPath: string): Promise<LicenseConfig | null> {\n  const licenseFiles = await getLicenseFiles(packager)\n  if (licenseFiles.length === 0) {\n    return null\n  }\n\n  const licenseButtonFiles = await getLicenseButtonsFile(packager)\n  packager.debugLogger.add(\"dmg.licenseFiles\", licenseFiles)\n  packager.debugLogger.add(\"dmg.licenseButtons\", licenseButtonFiles)\n\n  const jsonFile: LicenseConfig = {\n    $schema: \"https://github.com/argv-minus-one/dmg-license/raw/master/schema.json\",\n    // defaultLang: '',\n    body: [],\n    labels: [],\n  }\n\n  for (const file of licenseFiles) {\n    jsonFile.body.push({\n      file: file.file,\n      lang: file.langWithRegion.replace(\"_\", \"-\"),\n    })\n  }\n\n  for (const button of licenseButtonFiles) {\n    const filepath = button.file\n    const label = filepath.endsWith(\".yml\") ? load(await readFile(filepath, \"utf-8\")) : await readJson(filepath)\n    if (label.description) {\n      // to support original button file format\n      label.message = label.description\n      delete label.description\n    }\n    jsonFile.labels.push(\n      Object.assign(\n        {\n          lang: button.langWithRegion.replace(\"_\", \"-\"),\n        },\n        label\n      )\n    )\n  }\n\n  await dmgLicenseFromJSON(dmgPath, jsonFile, {\n    onNonFatalError: log.warn.bind(log),\n  })\n\n  return jsonFile\n}\n"]}