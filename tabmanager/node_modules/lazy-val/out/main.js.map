{"version": 3, "file": "main.js", "sourceRoot": "", "sources": ["../src/main.ts"], "names": [], "mappings": ";;;AAAA,MAAa,IAAI;IAIf,YAAY,OAAyB;QAH7B,WAAM,GAAsB,IAAI,CAAA;QAItC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;IACxB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAA;IAC7B,CAAC;IAED,IAAI,KAAK;QACP,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,OAAO,IAAI,CAAC,MAAQ,CAAA;SACrB;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC7B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAA;QACnB,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,KAAK,CAAC,KAAiB;QACzB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;IACrB,CAAC;CACF;AA1BD,oBA0BC", "sourcesContent": ["export class Lazy<T> {\n  private _value: Promise<T> | null = null\n  private creator: (() => Promise<T>) | null\n\n  constructor(creator: () => Promise<T>) {\n    this.creator = creator\n  }\n\n  get hasValue() {\n    return this.creator == null\n  }\n\n  get value(): Promise<T> {\n    if (this.creator == null) {\n      return this._value!!\n    }\n\n    const result = this.creator()\n    this.value = result\n    return result\n  }\n\n  set value(value: Promise<T>) {\n    this._value = value\n    this.creator = null\n  }\n}"]}