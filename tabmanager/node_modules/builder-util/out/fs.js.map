{"version": 3, "file": "fs.js", "sourceRoot": "", "sources": ["../src/fs.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,uCAAoD;AAEpD,2BAA6B;AAC7B,0CAAoH;AACpH,6BAA4B;AAC5B,yCAAgC;AAChC,+BAA2B;AAC3B,uCAAkE;AAClE,8BAA6B;AAEhB,QAAA,iBAAiB,GAAG,CAAC,CAAA;AACrB,QAAA,WAAW,GAAG,EAAE,WAAW,EAAE,yBAAiB,EAAE,CAAA;AAI7D,MAAa,mBAAmB;IAC9B,YAA4B,oBAA8C;QAA9C,yBAAoB,GAApB,oBAAoB,CAA0B;IAAG,CAAC;CAC/E;AAFD,kDAEC;AAKD,SAAgB,cAAc,CAAC,IAAY;IACzC,OAAO,IAAA,iBAAM,EAAC,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;QAC7B,YAAY;IACd,CAAC,CAAC,CAAA;AACJ,CAAC;AAJD,wCAIC;AAEM,KAAK,UAAU,UAAU,CAAC,IAAY;IAC3C,OAAO,IAAA,8BAAoB,EAAC,IAAA,eAAI,EAAC,IAAI,CAAC,CAAC,CAAA;AACzC,CAAC;AAFD,gCAEC;AAEM,KAAK,UAAU,MAAM,CAAC,IAAY;IACvC,IAAI,CAAC;QACH,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,CAAA;QAClB,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAPD,wBAOC;AAWD;;GAEG;AACI,KAAK,UAAU,IAAI,CAAC,cAAsB,EAAE,MAAsB,EAAE,QAAuB;IAChG,IAAI,MAAM,GAAkB,EAAE,CAAA;IAC9B,MAAM,KAAK,GAAkB,CAAC,cAAc,CAAC,CAAA;IAC7C,IAAI,cAAc,GAAG,KAAK,CAAA;IAC1B,MAAM,YAAY,GAAG,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAA;IAC9E,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxB,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;QAC5B,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,cAAc,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACtB,CAAC;iBAAM,CAAC;gBACN,cAAc,GAAG,IAAI,CAAA;YACvB,CAAC;QACH,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAgB,EAAC,IAAA,kBAAO,EAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAA;QAC/D,UAAU,CAAC,IAAI,EAAE,CAAA;QAEjB,IAAI,iBAAiB,GAAyB,IAAI,CAAA;QAElD,MAAM,IAAI,GAAkB,EAAE,CAAA;QAC9B,mHAAmH;QACnH,MAAM,eAAe,GAAG,MAAM,sBAAe,CAAC,GAAG,CAC/C,UAAU,EACV,IAAI,CAAC,EAAE;YACL,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;YAC1C,OAAO,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACjC,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;oBAC9C,OAAO,IAAI,CAAA;gBACb,CAAC;gBAED,MAAM,cAAc,GAAG,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;gBACtG,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;oBAC7B,OAAO,IAAI,CAAA;gBACb,CAAC;qBAAM,IAAI,cAAc,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,cAAc,CAAC,EAAE,CAAC;oBACjE,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,OAAO,IAAI,CAAA;oBACb,CAAC;yBAAM,CAAC;wBACN,OAAO,QAAQ,CAAA;oBACjB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAQ,cAA+B,CAAC,IAAI,CAAC,CAAC,EAAE,EAAO,EAAE;wBACvD,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;4BACpC,iBAAiB,GAAG,EAAE,CAAA;4BACtB,OAAO,IAAI,CAAA;wBACb,CAAC;wBAED,uDAAuD;wBACvD,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,aAAa,IAAI,EAAE,CAAC,CAAC,CAAE,EAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;4BAC7E,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;4BACf,OAAO,IAAI,CAAA;wBACb,CAAC;6BAAM,CAAC;4BACN,OAAO,QAAQ,CAAA;wBACjB,CAAC;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC,EACD,mBAAW,CACZ,CAAA;QAED,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAA;QACX,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;QACxC,CAAC;QAED,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAA;QAC3C,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAnFD,oBAmFC;AAED,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,OAAO,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,MAAM,CAAC,CAAA;AAEhJ,SAAgB,QAAQ,CAAC,GAAW,EAAE,IAAY,EAAE,WAAW,GAAG,IAAI;IACpE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;AAC9I,CAAC;AAFD,4BAEC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,GAAW,EAAE,IAAY,EAAE,KAAoB,EAAE,aAAuB,EAAE,iBAA0C;IACjJ,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;QAChC,aAAa,GAAG,cAAc,CAAA;IAChC,CAAC;IAED,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,MAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAA;QACrC,MAAM,IAAI,GAAG,IAAI,gBAAI,CAAC,KAAK,CAAC,CAAA;QAC5B,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAA;YACzB,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAA;QAC5B,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAA;QAEvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QAEnB,IAAI,kBAAkB,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;YACtC,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,IAAI,gBAAI,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAA;gBACtD,SAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,wBAAwB,CAAC,CAAA;YACpE,CAAC;YAED,uEAAuE;YACvE,+GAA+G;YAC/G,4IAA4I;YAC5I,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,GAAG,KAAK,CAAA;gBACrB,SAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,EAAE,mEAAmE,CAAC,CAAA;YAC1F,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAClB,OAAO,IAAA,eAAI,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;YACtC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBACvB,MAAM,KAAK,GAAG,iBAAiB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,EAAE,CAAA;gBACpE,IAAI,KAAK,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;oBAChC,SAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,EAAE,6BAA6B,CAAC,CAAA;gBAChE,CAAC;gBACD,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;YACrC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,CAAA;YACT,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IACD,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AACrC,CAAC;AAjDD,wCAiDC;AAED,SAAS,UAAU,CAAC,GAAW,EAAE,IAAY,EAAE,KAA+B;IAC5E,MAAM,OAAO,GAAG,IAAA,mBAAa,EAAC,GAAG,EAAE,IAAI,CAAC,CAAA;IACxC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,gBAAK,EAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;AACpD,CAAC;AAED,MAAa,UAAU;IAGrB,YACmB,qBAA0D,EAC1D,WAAoC;QADpC,0BAAqB,GAArB,qBAAqB,CAAqC;QAC1D,gBAAW,GAAX,WAAW,CAAyB;QAErD,IAAI,qBAAqB,KAAK,sBAAc,EAAE,CAAC;YAC7C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QAC3B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,aAAa,GAAG,cAAc,IAAI,qBAAqB,KAAK,6BAAqB,CAAA;QACxF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,GAAW,EAAE,IAAY,EAAE,IAAuB;QAC3D,IAAI,oBAAoB,GAAoC,IAAI,CAAA;QAChE,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9D,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;YAChC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;oBAC/C,IAAI,GAAG,MAAM,IAAI,CAAA;gBACnB,CAAC;gBAED,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,IAAI,IAAI,YAAY,mBAAmB,EAAE,CAAC;wBACxC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAA;oBAClD,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAA,oBAAS,EAAC,IAAI,EAAE,IAAI,CAAC,CAAA;wBAC3B,OAAM;oBACR,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,aAAa,GAAG,oBAAoB,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAA;QACzK,MAAM,cAAc,CAClB,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,aAAa,EACb,aAAa;YACX,CAAC,CAAC,GAAG,EAAE;gBACH,4KAA4K;gBAC5K,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;oBACvB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAA;oBAC1B,OAAO,IAAI,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;YACH,CAAC,CAAC,IAAI,CACT,CAAA;QAED,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACjC,MAAM,oBAAoB,CAAC,IAAI,CAAC,CAAA;QAClC,CAAC;IACH,CAAC;CACF;AAzDD,gCAyDC;AAQD;;;GAGG;AACH,SAAgB,OAAO,CAAC,GAAW,EAAE,WAAmB,EAAE,UAA0B,EAAE;IACpF,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,CAAC,CAAA;IAE7E,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;QACvB,SAAG,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,EAAE,UAAU,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;IAClG,CAAC;IAED,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAA;IAC3C,MAAM,KAAK,GAAgB,EAAE,CAAA;IAC7B,MAAM,WAAW,GAAG,IAAA,aAAQ,GAAE,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAChE,OAAO,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,EAAE;QAC/B,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC7C,OAAM;YACR,CAAC;YAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnC,MAAM,IAAA,gBAAK,EAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;gBAClE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;YAC/B,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,CAAC,CAAA;YAC/C,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClB,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YAC7C,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YAC5D,CAAC;QACH,CAAC;KACF,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,sBAAe,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,kBAAO,EAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,mBAAW,CAAC,CAAC,CAAA;AACtG,CAAC;AA7BD,0BA6BC;AAEM,KAAK,UAAU,OAAO,CAAC,OAAe;IAC3C,MAAM,OAAO,GAAG,MAAM,IAAA,kBAAO,EAAC,OAAO,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;IAE/D,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAA;QAEhD,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,CAAA;QACjC,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAA,eAAI,EAAC,SAAS,CAAC,CAAA;YACtC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,CAAC,CAAA;IACV,CAAC,CAAC,CAAA;IAEF,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,EAAE,CAAC,CAAC,CAAA;AACnG,CAAC;AAnBD,0BAmBC;AAED,6DAA6D;AACtD,MAAM,qBAAqB,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,KAAK,CAAA;AAA/C,QAAA,qBAAqB,yBAA0B;AAC5D,6DAA6D;AACtD,MAAM,cAAc,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,IAAI,CAAA;AAAvC,QAAA,cAAc,kBAAyB", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { copyFile as _nodeCopyFile } from \"fs-extra\"\nimport { Stats } from \"fs\"\nimport { platform } from \"os\"\nimport { access, chmod, mkdir, link, lstat, readdir, readlink, stat, symlink, unlink, writeFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { Mode } from \"stat-mode\"\nimport { log } from \"./log\"\nimport { orIfFileNotExist, orNullIfFileNotExist } from \"./promise\"\nimport * as isCI from \"is-ci\"\n\nexport const MAX_FILE_REQUESTS = 8\nexport const CONCURRENCY = { concurrency: MAX_FILE_REQUESTS }\n\nexport type AfterCopyFileTransformer = (file: string) => Promise<boolean>\n\nexport class CopyFileTransformer {\n  constructor(public readonly afterCopyTransformer: AfterCopyFileTransformer) {}\n}\n\nexport type FileTransformer = (file: string) => Promise<null | string | Buffer | CopyFileTransformer> | null | string | Buffer | CopyFileTransformer\nexport type Filter = (file: string, stat: Stats) => boolean\n\nexport function unlinkIfExists(file: string) {\n  return unlink(file).catch(() => {\n    /* ignore */\n  })\n}\n\nexport async function statOrNull(file: string): Promise<Stats | null> {\n  return orNullIfFileNotExist(stat(file))\n}\n\nexport async function exists(file: string): Promise<boolean> {\n  try {\n    await access(file)\n    return true\n  } catch (e: any) {\n    return false\n  }\n}\n\nexport interface FileConsumer {\n  consume(file: string, fileStat: Stats, parent: string, siblingNames: Array<string>): any\n\n  /**\n   * @default false\n   */\n  isIncludeDir?: boolean\n}\n\n/**\n * Returns list of file paths (system-dependent file separator)\n */\nexport async function walk(initialDirPath: string, filter?: Filter | null, consumer?: FileConsumer): Promise<Array<string>> {\n  let result: Array<string> = []\n  const queue: Array<string> = [initialDirPath]\n  let addDirToResult = false\n  const isIncludeDir = consumer == null ? false : consumer.isIncludeDir === true\n  while (queue.length > 0) {\n    const dirPath = queue.pop()!\n    if (isIncludeDir) {\n      if (addDirToResult) {\n        result.push(dirPath)\n      } else {\n        addDirToResult = true\n      }\n    }\n\n    const childNames = await orIfFileNotExist(readdir(dirPath), [])\n    childNames.sort()\n\n    let nodeModuleContent: Array<string> | null = null\n\n    const dirs: Array<string> = []\n    // our handler is async, but we should add sorted files, so, we add file to result not in the mapper, but after map\n    const sortedFilePaths = await BluebirdPromise.map(\n      childNames,\n      name => {\n        if (name === \".DS_Store\" || name === \".gitkeep\") {\n          return null\n        }\n\n        const filePath = dirPath + path.sep + name\n        return lstat(filePath).then(stat => {\n          if (filter != null && !filter(filePath, stat)) {\n            return null\n          }\n\n          const consumerResult = consumer == null ? null : consumer.consume(filePath, stat, dirPath, childNames)\n          if (consumerResult === false) {\n            return null\n          } else if (consumerResult == null || !(\"then\" in consumerResult)) {\n            if (stat.isDirectory()) {\n              dirs.push(name)\n              return null\n            } else {\n              return filePath\n            }\n          } else {\n            return (consumerResult as Promise<any>).then((it): any => {\n              if (it != null && Array.isArray(it)) {\n                nodeModuleContent = it\n                return null\n              }\n\n              // asarUtil can return modified stat (symlink handling)\n              if ((it != null && \"isDirectory\" in it ? (it as Stats) : stat).isDirectory()) {\n                dirs.push(name)\n                return null\n              } else {\n                return filePath\n              }\n            })\n          }\n        })\n      },\n      CONCURRENCY\n    )\n\n    for (const child of sortedFilePaths) {\n      if (child != null) {\n        result.push(child)\n      }\n    }\n\n    dirs.sort()\n    for (const child of dirs) {\n      queue.push(dirPath + path.sep + child)\n    }\n\n    if (nodeModuleContent != null) {\n      result = result.concat(nodeModuleContent)\n    }\n  }\n\n  return result\n}\n\nconst _isUseHardLink = process.platform !== \"win32\" && process.env.USE_HARD_LINKS !== \"false\" && (isCI || process.env.USE_HARD_LINKS === \"true\")\n\nexport function copyFile(src: string, dest: string, isEnsureDir = true) {\n  return (isEnsureDir ? mkdir(path.dirname(dest), { recursive: true }) : Promise.resolve()).then(() => copyOrLinkFile(src, dest, null, false))\n}\n\n/**\n * Hard links is used if supported and allowed.\n * File permission is fixed — allow execute for all if owner can, allow read for all if owner can.\n *\n * ensureDir is not called, dest parent dir must exists\n */\nexport function copyOrLinkFile(src: string, dest: string, stats?: Stats | null, isUseHardLink?: boolean, exDevErrorHandler?: (() => boolean) | null): Promise<any> {\n  if (isUseHardLink === undefined) {\n    isUseHardLink = _isUseHardLink\n  }\n\n  if (stats != null) {\n    const originalModeNumber = stats.mode\n    const mode = new Mode(stats)\n    if (mode.owner.execute) {\n      mode.group.execute = true\n      mode.others.execute = true\n    }\n\n    mode.group.read = true\n    mode.others.read = true\n\n    mode.setuid = false\n    mode.setgid = false\n\n    if (originalModeNumber !== stats.mode) {\n      if (log.isDebugEnabled) {\n        const oldMode = new Mode({ mode: originalModeNumber })\n        log.debug({ file: dest, oldMode, mode }, \"permissions fixed from\")\n      }\n\n      // https://helgeklein.com/blog/2009/05/hard-links-and-permissions-acls/\n      // Permissions on all hard links to the same data on disk are always identical. The same applies to attributes.\n      // That means if you change the permissions/owner/attributes on one hard link, you will immediately see the changes on all other hard links.\n      if (isUseHardLink) {\n        isUseHardLink = false\n        log.debug({ dest }, \"copied, but not linked, because file permissions need to be fixed\")\n      }\n    }\n  }\n\n  if (isUseHardLink) {\n    return link(src, dest).catch((e: any) => {\n      if (e.code === \"EXDEV\") {\n        const isLog = exDevErrorHandler == null ? true : exDevErrorHandler()\n        if (isLog && log.isDebugEnabled) {\n          log.debug({ error: e.message }, \"cannot copy using hard link\")\n        }\n        return doCopyFile(src, dest, stats)\n      } else {\n        throw e\n      }\n    })\n  }\n  return doCopyFile(src, dest, stats)\n}\n\nfunction doCopyFile(src: string, dest: string, stats: Stats | null | undefined): Promise<any> {\n  const promise = _nodeCopyFile(src, dest)\n  if (stats == null) {\n    return promise\n  }\n\n  return promise.then(() => chmod(dest, stats.mode))\n}\n\nexport class FileCopier {\n  isUseHardLink: boolean\n\n  constructor(\n    private readonly isUseHardLinkFunction?: ((file: string) => boolean) | null,\n    private readonly transformer?: FileTransformer | null\n  ) {\n    if (isUseHardLinkFunction === USE_HARD_LINKS) {\n      this.isUseHardLink = true\n    } else {\n      this.isUseHardLink = _isUseHardLink && isUseHardLinkFunction !== DO_NOT_USE_HARD_LINKS\n    }\n  }\n\n  async copy(src: string, dest: string, stat: Stats | undefined): Promise<void> {\n    let afterCopyTransformer: AfterCopyFileTransformer | null = null\n    if (this.transformer != null && stat != null && stat.isFile()) {\n      let data = this.transformer(src)\n      if (data != null) {\n        if (typeof data === \"object\" && \"then\" in data) {\n          data = await data\n        }\n\n        if (data != null) {\n          if (data instanceof CopyFileTransformer) {\n            afterCopyTransformer = data.afterCopyTransformer\n          } else {\n            await writeFile(dest, data)\n            return\n          }\n        }\n      }\n    }\n\n    const isUseHardLink = afterCopyTransformer == null && (!this.isUseHardLink || this.isUseHardLinkFunction == null ? this.isUseHardLink : this.isUseHardLinkFunction(dest))\n    await copyOrLinkFile(\n      src,\n      dest,\n      stat,\n      isUseHardLink,\n      isUseHardLink\n        ? () => {\n            // files are copied concurrently, so, we must not check here currentIsUseHardLink — our code can be executed after that other handler will set currentIsUseHardLink to false\n            if (this.isUseHardLink) {\n              this.isUseHardLink = false\n              return true\n            } else {\n              return false\n            }\n          }\n        : null\n    )\n\n    if (afterCopyTransformer != null) {\n      await afterCopyTransformer(dest)\n    }\n  }\n}\n\nexport interface CopyDirOptions {\n  filter?: Filter | null\n  transformer?: FileTransformer | null\n  isUseHardLink?: ((file: string) => boolean) | null\n}\n\n/**\n * Empty directories is never created.\n * Hard links is used if supported and allowed.\n */\nexport function copyDir(src: string, destination: string, options: CopyDirOptions = {}): Promise<any> {\n  const fileCopier = new FileCopier(options.isUseHardLink, options.transformer)\n\n  if (log.isDebugEnabled) {\n    log.debug({ src, destination }, `copying${fileCopier.isUseHardLink ? \" using hard links\" : \"\"}`)\n  }\n\n  const createdSourceDirs = new Set<string>()\n  const links: Array<Link> = []\n  const symlinkType = platform() === \"win32\" ? \"junction\" : \"file\"\n  return walk(src, options.filter, {\n    consume: async (file, stat, parent) => {\n      if (!stat.isFile() && !stat.isSymbolicLink()) {\n        return\n      }\n\n      if (!createdSourceDirs.has(parent)) {\n        await mkdir(parent.replace(src, destination), { recursive: true })\n        createdSourceDirs.add(parent)\n      }\n\n      const destFile = file.replace(src, destination)\n      if (stat.isFile()) {\n        await fileCopier.copy(file, destFile, stat)\n      } else {\n        links.push({ file: destFile, link: await readlink(file) })\n      }\n    },\n  }).then(() => BluebirdPromise.map(links, it => symlink(it.link, it.file, symlinkType), CONCURRENCY))\n}\n\nexport async function dirSize(dirPath: string): Promise<number> {\n  const entries = await readdir(dirPath, { withFileTypes: true })\n\n  const entrySizes = entries.map(async entry => {\n    const entryPath = path.join(dirPath, entry.name)\n\n    if (entry.isDirectory()) {\n      return await dirSize(entryPath)\n    }\n\n    if (entry.isFile()) {\n      const { size } = await stat(entryPath)\n      return size\n    }\n\n    return 0\n  })\n\n  return (await Promise.all(entrySizes)).reduce((entrySize, totalSize) => entrySize + totalSize, 0)\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport const DO_NOT_USE_HARD_LINKS = (file: string) => false\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport const USE_HARD_LINKS = (file: string) => true\n\nexport interface Link {\n  readonly link: string\n  readonly file: string\n}\n"]}