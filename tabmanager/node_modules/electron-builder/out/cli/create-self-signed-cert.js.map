{"version": 3, "file": "create-self-signed-cert.js", "sourceRoot": "", "sources": ["../../src/cli/create-self-signed-cert.ts"], "names": [], "mappings": ";;;AAAA,gEAAoE;AACpE,+CAAuD;AACvD,4CAAoD;AACpD,+BAA8B;AAC9B,kFAAgF;AAChF,0CAAmC;AACnC,6BAA4B;AAE5B,gBAAgB;AACT,KAAK,UAAU,oBAAoB,CAAC,SAAiB;IAC1D,MAAM,MAAM,GAAG,IAAI,qBAAM,CAAC,yBAAyB,CAAC,CAAA;IACpD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,0BAA0B,EAAE,CAAC,EAAE,IAAA,2BAAgB,EAAC,SAAS,CAAC,CAAC,CAAA;IAC1H,MAAM,GAAG,GAAG,GAAG,UAAU,MAAM,CAAA;IAC/B,MAAM,GAAG,GAAG,GAAG,UAAU,MAAM,CAAA;IAE/B,kBAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,uFAAuF,CAAC,CAAC,CAAA;IAE7G,IAAI,CAAC;QACH,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAA,mCAAiB,GAAE,EAAE,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QACnF,MAAM,IAAA,mBAAI,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,mBAAmB,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;QAE/J,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAA,2BAAgB,EAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QACtE,MAAM,IAAA,mBAAc,EAAC,GAAG,CAAC,CAAA;QACzB,MAAM,IAAA,mBAAI,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAA;QACzF,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,gFAAgF,CAAC,CAAA;QAEzG,MAAM,YAAY,GAAG,oCAAoC,CAAA;QACzD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,gGAAgG,CAAC,CAAA;QACvI,MAAM,IAAA,oBAAK,EAAC,gBAAgB,EAAE,CAAC,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,uBAAuB,EAAE,WAAW,EAAE,IAAI,GAAG,GAAG,EAAE,oBAAoB,EAAE,YAAY,CAAC,CAAC,CAAA;IACpK,CAAC;YAAS,CAAC;QACT,MAAM,MAAM,CAAC,OAAO,EAAE,CAAA;IACxB,CAAC;AACH,CAAC;AAzBD,oDAyBC;AAED,SAAS,WAAW,CAAC,CAAS;IAC5B,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACzC,OAAO,CAAC,CAAA;IACV,CAAC;IAED,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAA;AACtC,CAAC", "sourcesContent": ["import { sanitizeFileName } from \"app-builder-lib/out/util/filename\"\nimport { exec, log, spawn, TmpDir } from \"builder-util\"\nimport { unlinkIfExists } from \"builder-util/out/fs\"\nimport * as chalk from \"chalk\"\nimport { getSignVendorPath } from \"app-builder-lib/out/codeSign/windowsCodeSign\"\nimport { mkdir } from \"fs/promises\"\nimport * as path from \"path\"\n\n/** @internal */\nexport async function createSelfSignedCert(publisher: string) {\n  const tmpDir = new TmpDir(\"create-self-signed-cert\")\n  const targetDir = process.cwd()\n  const tempPrefix = path.join(await tmpDir.getTempDir({ prefix: \"self-signed-cert-creator\" }), sanitizeFileName(publisher))\n  const cer = `${tempPrefix}.cer`\n  const pvk = `${tempPrefix}.pvk`\n\n  log.info(chalk.bold('When asked to enter a password (\"Create Private Key Password\"), please select \"None\".'))\n\n  try {\n    await mkdir(path.dirname(tempPrefix), { recursive: true })\n    const vendorPath = path.join(await getSignVendorPath(), \"windows-10\", process.arch)\n    await exec(path.join(vendorPath, \"makecert.exe\"), [\"-r\", \"-h\", \"0\", \"-n\", `CN=${quoteString(publisher)}`, \"-eku\", \"1.3.6.1.5.5.7.3.3\", \"-pe\", \"-sv\", pvk, cer])\n\n    const pfx = path.join(targetDir, `${sanitizeFileName(publisher)}.pfx`)\n    await unlinkIfExists(pfx)\n    await exec(path.join(vendorPath, \"pvk2pfx.exe\"), [\"-pvk\", pvk, \"-spc\", cer, \"-pfx\", pfx])\n    log.info({ file: pfx }, `created. Please see https://electron.build/code-signing how to use it to sign.`)\n\n    const certLocation = \"Cert:\\\\LocalMachine\\\\TrustedPeople\"\n    log.info({ file: pfx, certLocation }, `importing. Operation will be succeed only if runned from root. Otherwise import file manually.`)\n    await spawn(\"powershell.exe\", [\"-NoProfile\", \"-NonInteractive\", \"-Command\", \"Import-PfxCertificate\", \"-FilePath\", `\"${pfx}\"`, \"-CertStoreLocation\", certLocation])\n  } finally {\n    await tmpDir.cleanup()\n  }\n}\n\nfunction quoteString(s: string): string {\n  if (!s.includes(\",\") && !s.includes('\"')) {\n    return s\n  }\n\n  return `\"${s.replace(/\"/g, '\\\\\"')}\"`\n}\n"]}