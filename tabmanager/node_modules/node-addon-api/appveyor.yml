environment:
  # https://github.com/jasongin/nvs/blob/master/doc/CI.md
  NVS_VERSION: 1.4.2
  fast_finish: true
  matrix:
    - NODEJS_VERSION: node/4
    - NODEJS_VERSION: node/6
    - NODEJS_VERSION: node/8
    - NODEJS_VERSION: node/9
    - NODEJS_VERSION: node/10
    - NODEJS_VERSION: chakracore/8
    - NODEJS_VERSION: chakracore/10
    - NODEJS_VERSION: nightly
    - NODEJS_VERSION: chakracore-nightly

matrix:
  fast_finish: true
  allow_failures:
    - NODEJS_VERSION: nightly
    - NODEJS_VERSION: chakracore-nightly

platform:
  - x86
  - x64

install:
  # nvs
  - git clone --branch v%NVS_VERSION% --depth 1 https://github.com/jasongin/nvs %LOCALAPPDATA%\nvs
  - set PATH=%LOCALAPPDATA%\nvs;%PATH%
  - nvs --version
  # node.js
  - nvs add %NODEJS_VERSION%/%PLATFORM%
  - nvs use %NODEJS_VERSION%/%PLATFORM%
  - node --version
  - node -p process.arch
  - npm --version
  # app
  - npm install

test_script:
  - npm test

build: off

version: "{build}"

cache:
  - node_modules
