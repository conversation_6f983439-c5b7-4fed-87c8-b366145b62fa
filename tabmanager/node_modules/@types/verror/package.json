{"name": "@types/verror", "version": "1.10.11", "description": "TypeScript definitions for verror", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/verror", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "max4t", "url": "https://github.com/max4t"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/verror"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "da8f137bf4c525dd0e44b6c69e5049794a923e6ef00945327ca62697ef200b9e", "typeScriptVersion": "5.0"}