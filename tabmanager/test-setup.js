#!/usr/bin/env node

// Test script to verify Tab Manager setup
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class SetupTester {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  async runTests() {
    console.log('🧪 Testing Tab Manager setup...\n');

    this.testFileStructure();
    this.testPackageJson();
    this.testExtensionManifests();
    this.testNativeHost();
    await this.testDependencies();

    this.printResults();
  }

  testFileStructure() {
    console.log('📁 Checking file structure...');
    
    const requiredFiles = [
      'package.json',
      'src/main.js',
      'src/preload.js',
      'src/renderer/index.html',
      'src/renderer/styles.css',
      'src/renderer/renderer.js',
      'extensions/chrome/manifest.json',
      'extensions/chrome/background.js',
      'extensions/edge/manifest.json',
      'extensions/edge/background.js',
      'native-host/host.js',
      'native-host/install.js'
    ];

    requiredFiles.forEach(file => {
      const filePath = path.join(__dirname, file);
      if (!fs.existsSync(filePath)) {
        this.errors.push(`Missing required file: ${file}`);
      }
    });

    if (this.errors.length === 0) {
      console.log('✅ All required files present\n');
    }
  }

  testPackageJson() {
    console.log('📦 Checking package.json...');
    
    try {
      const packagePath = path.join(__dirname, 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const requiredScripts = ['start', 'dev', 'build'];
      requiredScripts.forEach(script => {
        if (!packageJson.scripts || !packageJson.scripts[script]) {
          this.errors.push(`Missing script: ${script}`);
        }
      });

      const requiredDeps = ['electron'];
      requiredDeps.forEach(dep => {
        if (!packageJson.devDependencies || !packageJson.devDependencies[dep]) {
          this.errors.push(`Missing dependency: ${dep}`);
        }
      });

      if (this.errors.length === 0) {
        console.log('✅ package.json looks good\n');
      }
    } catch (error) {
      this.errors.push(`Failed to parse package.json: ${error.message}`);
    }
  }

  testExtensionManifests() {
    console.log('🔌 Checking extension manifests...');
    
    const manifests = [
      'extensions/chrome/manifest.json',
      'extensions/edge/manifest.json'
    ];

    manifests.forEach(manifestPath => {
      try {
        const fullPath = path.join(__dirname, manifestPath);
        const manifest = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
        
        if (manifest.manifest_version !== 3) {
          this.warnings.push(`${manifestPath}: Should use manifest v3`);
        }

        const requiredPermissions = ['tabs', 'nativeMessaging'];
        requiredPermissions.forEach(permission => {
          if (!manifest.permissions || !manifest.permissions.includes(permission)) {
            this.errors.push(`${manifestPath}: Missing permission: ${permission}`);
          }
        });

      } catch (error) {
        this.errors.push(`Failed to parse ${manifestPath}: ${error.message}`);
      }
    });

    if (this.errors.length === 0) {
      console.log('✅ Extension manifests look good\n');
    }
  }

  testNativeHost() {
    console.log('🌉 Checking native messaging host...');
    
    const hostPath = path.join(__dirname, 'native-host/host.js');
    
    try {
      // Check if file is executable (on Unix systems)
      if (process.platform !== 'win32') {
        const stats = fs.statSync(hostPath);
        const isExecutable = !!(stats.mode & parseInt('111', 8));
        if (!isExecutable) {
          this.warnings.push('native-host/host.js is not executable. Run: chmod +x native-host/host.js');
        }
      }

      // Check shebang
      const content = fs.readFileSync(hostPath, 'utf8');
      if (!content.startsWith('#!/usr/bin/env node')) {
        this.warnings.push('native-host/host.js missing proper shebang');
      }

      console.log('✅ Native host script looks good\n');
    } catch (error) {
      this.errors.push(`Failed to check native host: ${error.message}`);
    }
  }

  async testDependencies() {
    console.log('📚 Checking dependencies...');
    
    return new Promise((resolve) => {
      const npm = spawn('npm', ['list', '--depth=0'], {
        cwd: __dirname,
        stdio: 'pipe'
      });

      let output = '';
      npm.stdout.on('data', (data) => {
        output += data.toString();
      });

      npm.stderr.on('data', (data) => {
        output += data.toString();
      });

      npm.on('close', (code) => {
        if (code !== 0) {
          this.warnings.push('Some dependencies may be missing. Run: npm install');
        } else {
          console.log('✅ Dependencies installed\n');
        }
        resolve();
      });

      npm.on('error', (error) => {
        this.warnings.push(`Could not check dependencies: ${error.message}`);
        resolve();
      });
    });
  }

  printResults() {
    console.log('📊 Test Results:');
    console.log('================\n');

    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('🎉 All tests passed! Your Tab Manager setup looks good.\n');
      console.log('Next steps:');
      console.log('1. Install dependencies: npm install');
      console.log('2. Install native messaging host: cd native-host && node install.js install');
      console.log('3. Install browser extensions (see README.md)');
      console.log('4. Start the app: npm start');
    } else {
      if (this.errors.length > 0) {
        console.log('❌ Errors found:');
        this.errors.forEach(error => console.log(`   • ${error}`));
        console.log();
      }

      if (this.warnings.length > 0) {
        console.log('⚠️  Warnings:');
        this.warnings.forEach(warning => console.log(`   • ${warning}`));
        console.log();
      }

      console.log('Please fix the issues above before proceeding.');
    }
  }
}

// Run the tests
const tester = new SetupTester();
tester.runTests().catch(console.error);
