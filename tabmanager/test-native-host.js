#!/usr/bin/env node

// Test script to verify native messaging host works
const { spawn } = require('child_process');
const path = require('path');

console.log('Testing native messaging host...');

const hostPath = path.join(__dirname, 'native-host', 'host.js');
console.log('Host path:', hostPath);

// Spawn the native host
const host = spawn('node', [hostPath], {
  stdio: ['pipe', 'pipe', 'pipe']
});

let responseReceived = false;

// Listen for output
host.stdout.on('data', (data) => {
  console.log('Host stdout:', data.toString());
  responseReceived = true;
});

host.stderr.on('data', (data) => {
  console.log('Host stderr:', data.toString());
});

host.on('close', (code) => {
  console.log(`Host process exited with code ${code}`);
  if (!responseReceived) {
    console.log('❌ No response received from host');
  }
});

host.on('error', (error) => {
  console.error('❌ Failed to start host:', error);
});

// Send a test message
const testMessage = JSON.stringify({
  action: 'getTabs'
});

const messageLength = Buffer.byteLength(testMessage, 'utf8');
const lengthBuffer = Buffer.allocUnsafe(4);
lengthBuffer.writeUInt32LE(messageLength, 0);

console.log('Sending test message:', testMessage);

// Send length header + message
host.stdin.write(lengthBuffer);
host.stdin.write(testMessage, 'utf8');

// Close after 3 seconds
setTimeout(() => {
  console.log('Closing host...');
  host.kill();
  
  if (responseReceived) {
    console.log('✅ Native host is working!');
  } else {
    console.log('❌ Native host not responding properly');
  }
}, 3000);
