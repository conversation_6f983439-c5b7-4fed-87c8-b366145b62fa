const { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

class TabManager {
  constructor() {
    this.mainWindow = null;
    this.nativeHosts = new Map();
    this.tabs = [];
  }

  async createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1000,
      height: 700,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      title: 'Tab Manager',
      show: false
    });

    await this.mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      // Stop polling when window is closed
      if (this.pollInterval) {
        clearInterval(this.pollInterval);
      }
    });

    // Set up menu
    this.createMenu();

    // Start polling for tab updates every 2 seconds
    this.startPolling();
  }

  startPolling() {
    this.pollInterval = setInterval(() => {
      this.refreshTabs();
    }, 2000);
  }

  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Refresh Tabs',
            accelerator: 'CmdOrCtrl+R',
            click: () => {
              this.refreshTabs();
            }
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  async refreshTabs() {
    try {
      // Read tab data from the file written by the native messaging host
      const dataFile = path.join(__dirname, '../native-host/tabs-data.json');

      let tabData = null;
      if (fs.existsSync(dataFile)) {
        try {
          const fileContent = fs.readFileSync(dataFile, 'utf8');
          tabData = JSON.parse(fileContent);
          console.log('Loaded tab data from native host:', tabData.tabs?.length || 0, 'tabs');
        } catch (parseError) {
          console.error('Error parsing tab data file:', parseError);
        }
      }

      if (tabData && tabData.tabs) {
        this.tabs = tabData.tabs;
      } else {
        // Fallback to mock data if no real data available
        console.log('No tab data available, using mock data');
        this.tabs = [
          {
            id: 'chrome-1',
            title: 'Google',
            url: 'https://www.google.com',
            browser: 'chrome',
            favicon: ''
          },
          {
            id: 'chrome-2',
            title: 'GitHub - No real tabs detected',
            url: 'https://github.com',
            browser: 'chrome',
            favicon: ''
          }
        ];
      }

      if (this.mainWindow) {
        this.mainWindow.webContents.send('tabs-updated', this.tabs);
      }
    } catch (error) {
      console.error('Error refreshing tabs:', error);
    }
  }

  async getTabsFromBrowser(browserType) {
    return new Promise((resolve) => {
      // Check if we have cached tabs from native messaging
      const cachedTabs = this.tabs.filter(tab => tab.browser === browserType);
      resolve(cachedTabs);
    });
  }

  // Method to receive tabs from native messaging host
  updateTabsFromNativeHost(allTabs) {
    this.tabs = allTabs;

    if (this.mainWindow) {
      this.mainWindow.webContents.send('tabs-updated', this.tabs);
    }
  }

  // Find and return duplicate tabs grouped by URL
  findDuplicateTabs() {
    const urlGroups = {};
    const duplicates = [];

    // Group tabs by URL
    this.tabs.forEach(tab => {
      const url = tab.url.toLowerCase().trim();
      if (!urlGroups[url]) {
        urlGroups[url] = [];
      }
      urlGroups[url].push(tab);
    });

    // Find URLs with multiple tabs
    Object.entries(urlGroups).forEach(([url, tabs]) => {
      if (tabs.length > 1) {
        // Sort by: pinned first, then active, then by window ID
        tabs.sort((a, b) => {
          if (a.pinned !== b.pinned) return b.pinned ? 1 : -1;
          if (a.active !== b.active) return b.active ? 1 : -1;
          return a.windowId - b.windowId;
        });

        duplicates.push({
          url: url,
          tabs: tabs,
          keepTab: tabs[0], // Keep the first one (pinned/active/first window)
          duplicateCount: tabs.length - 1
        });
      }
    });

    return duplicates;
  }

  // Close duplicate tabs automatically
  async closeDuplicateTabs(options = {}) {
    const {
      dryRun = false,
      excludePinned = true,
      excludeActive = true
    } = options;

    const duplicates = this.findDuplicateTabs();
    let closedCount = 0;
    const results = [];

    for (const duplicate of duplicates) {
      const tabsToClose = duplicate.tabs.slice(1); // Skip the first one (keep it)

      for (const tab of tabsToClose) {
        // Apply exclusion rules
        if (excludePinned && tab.pinned) continue;
        if (excludeActive && tab.active) continue;

        if (!dryRun) {
          try {
            await this.closeTab(tab.id, tab.browser);
            closedCount++;
            console.log(`Closed duplicate tab: ${tab.title} (${tab.url})`);
          } catch (error) {
            console.error(`Failed to close tab ${tab.id}:`, error);
          }
        } else {
          closedCount++;
        }
      }

      results.push({
        url: duplicate.url,
        totalTabs: duplicate.tabs.length,
        closedTabs: tabsToClose.filter(tab =>
          !(excludePinned && tab.pinned) &&
          !(excludeActive && tab.active)
        ).length,
        keptTab: duplicate.keepTab
      });
    }

    return {
      duplicateGroups: duplicates.length,
      totalClosed: closedCount,
      results: results
    };
  }

  async closeTab(tabId, browserType) {
    console.log(`Closing tab ${tabId} in ${browserType}`);

    try {
      // Send close command to native messaging host via the data file
      const commandFile = path.join(__dirname, '../native-host/commands.json');
      const command = {
        action: 'closeTab',
        tabId: tabId,
        browser: browserType,
        timestamp: Date.now()
      };

      fs.writeFileSync(commandFile, JSON.stringify(command));
      console.log(`Sent close command for tab ${tabId}`);

      // Remove from local array immediately for UI responsiveness
      this.tabs = this.tabs.filter(tab => tab.id !== tabId);

      if (this.mainWindow) {
        this.mainWindow.webContents.send('tabs-updated', this.tabs);
      }

      // Refresh tabs after a short delay to get updated state
      setTimeout(() => {
        this.refreshTabs();
      }, 1000);

    } catch (error) {
      console.error('Error closing tab:', error);
    }
  }
}

const tabManager = new TabManager();

app.whenReady().then(() => {
  tabManager.createWindow();
  
  // Initial tab refresh
  setTimeout(() => {
    tabManager.refreshTabs();
  }, 1000);
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    tabManager.createWindow();
  }
});

// IPC handlers
ipcMain.handle('get-tabs', async () => {
  return tabManager.tabs;
});

ipcMain.handle('refresh-tabs', async () => {
  await tabManager.refreshTabs();
  return tabManager.tabs;
});

ipcMain.handle('close-tab', async (event, tabId, browserType) => {
  await tabManager.closeTab(tabId, browserType);
  return true;
});

ipcMain.handle('search-tabs', async (event, query) => {
  const filteredTabs = tabManager.tabs.filter(tab =>
    tab.title.toLowerCase().includes(query.toLowerCase()) ||
    tab.url.toLowerCase().includes(query.toLowerCase())
  );
  return filteredTabs;
});

ipcMain.handle('find-duplicates', async () => {
  return tabManager.findDuplicateTabs();
});

ipcMain.handle('close-duplicates', async (event, options) => {
  return await tabManager.closeDuplicateTabs(options);
});
