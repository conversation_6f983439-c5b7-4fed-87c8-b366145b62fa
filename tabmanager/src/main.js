const { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

class TabManager {
  constructor() {
    this.mainWindow = null;
    this.nativeHosts = new Map();
    this.tabs = [];
  }

  async createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1000,
      height: 700,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js')
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      title: 'Tab Manager',
      show: false
    });

    await this.mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));
    
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Set up menu
    this.createMenu();
  }

  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Refresh Tabs',
            accelerator: 'CmdOrCtrl+R',
            click: () => {
              this.refreshTabs();
            }
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  async refreshTabs() {
    try {
      const chromeTabs = await this.getTabsFromBrowser('chrome');
      const edgeTabs = await this.getTabsFromBrowser('edge');
      
      this.tabs = [...chromeTabs, ...edgeTabs];
      
      if (this.mainWindow) {
        this.mainWindow.webContents.send('tabs-updated', this.tabs);
      }
    } catch (error) {
      console.error('Error refreshing tabs:', error);
    }
  }

  async getTabsFromBrowser(browserType) {
    return new Promise((resolve) => {
      // Check if we have cached tabs from native messaging
      const cachedTabs = this.tabs.filter(tab => tab.browser === browserType);
      resolve(cachedTabs);
    });
  }

  // Method to receive tabs from native messaging host
  updateTabsFromNativeHost(allTabs) {
    this.tabs = allTabs;

    if (this.mainWindow) {
      this.mainWindow.webContents.send('tabs-updated', this.tabs);
    }
  }

  async closeTab(tabId, browserType) {
    // Send close command to native messaging host
    console.log(`Closing tab ${tabId} in ${browserType}`);

    // In a real implementation, this would send a message to the native host
    // For now, just remove from local array
    this.tabs = this.tabs.filter(tab => tab.id !== tabId);

    if (this.mainWindow) {
      this.mainWindow.webContents.send('tabs-updated', this.tabs);
    }
  }
}

const tabManager = new TabManager();

app.whenReady().then(() => {
  tabManager.createWindow();
  
  // Initial tab refresh
  setTimeout(() => {
    tabManager.refreshTabs();
  }, 1000);
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    tabManager.createWindow();
  }
});

// IPC handlers
ipcMain.handle('get-tabs', async () => {
  return tabManager.tabs;
});

ipcMain.handle('refresh-tabs', async () => {
  await tabManager.refreshTabs();
  return tabManager.tabs;
});

ipcMain.handle('close-tab', async (event, tabId, browserType) => {
  await tabManager.closeTab(tabId, browserType);
  return true;
});

ipcMain.handle('search-tabs', async (event, query) => {
  const filteredTabs = tabManager.tabs.filter(tab => 
    tab.title.toLowerCase().includes(query.toLowerCase()) ||
    tab.url.toLowerCase().includes(query.toLowerCase())
  );
  return filteredTabs;
});
