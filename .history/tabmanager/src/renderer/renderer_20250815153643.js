class TabManagerUI {
    constructor() {
        this.tabs = [];
        this.filteredTabs = [];
        this.duplicates = [];
        this.currentFilter = 'all';
        this.currentSort = 'title';
        this.searchQuery = '';
        this.showingDuplicates = false;

        this.initializeElements();
        this.bindEvents();
        this.loadTabs();
    }

    initializeElements() {
        this.searchInput = document.getElementById('searchInput');
        this.clearSearchBtn = document.getElementById('clearSearch');
        this.refreshBtn = document.getElementById('refreshBtn');
        this.tabsList = document.getElementById('tabsList');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.emptyState = document.getElementById('emptyState');
        this.tabCount = document.getElementById('tabCount');
        this.duplicateCount = document.getElementById('duplicateCount');
        this.sortSelect = document.getElementById('sortSelect');
        this.filterBtns = document.querySelectorAll('.filter-btn');

        // Duplicate management elements
        this.findDuplicatesBtn = document.getElementById('findDuplicatesBtn');
        this.closeDuplicatesBtn = document.getElementById('closeDuplicatesBtn');
        this.duplicateOptions = document.getElementById('duplicateOptions');
        this.previewDuplicatesBtn = document.getElementById('previewDuplicatesBtn');
        this.confirmCloseDuplicatesBtn = document.getElementById('confirmCloseDuplicatesBtn');
        this.cancelDuplicatesBtn = document.getElementById('cancelDuplicatesBtn');
        this.excludePinnedCheckbox = document.getElementById('excludePinned');
        this.excludeActiveCheckbox = document.getElementById('excludeActive');
    }

    bindEvents() {
        // Search functionality
        this.searchInput.addEventListener('input', (e) => {
            this.searchQuery = e.target.value;
            this.filterAndDisplayTabs();
        });

        this.clearSearchBtn.addEventListener('click', () => {
            this.searchInput.value = '';
            this.searchQuery = '';
            this.filterAndDisplayTabs();
        });

        // Refresh button
        this.refreshBtn.addEventListener('click', () => {
            this.loadTabs();
        });

        // Sort functionality
        this.sortSelect.addEventListener('change', (e) => {
            this.currentSort = e.target.value;
            this.filterAndDisplayTabs();
        });

        // Filter buttons
        this.filterBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.filterBtns.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentFilter = e.target.dataset.browser;
                this.filterAndDisplayTabs();
            });
        });

        // Duplicate management events
        this.findDuplicatesBtn.addEventListener('click', () => {
            this.findDuplicates();
        });

        this.closeDuplicatesBtn.addEventListener('click', () => {
            this.showDuplicateOptions();
        });

        this.previewDuplicatesBtn.addEventListener('click', () => {
            this.previewDuplicateRemoval();
        });

        this.confirmCloseDuplicatesBtn.addEventListener('click', () => {
            this.closeDuplicates();
        });

        this.cancelDuplicatesBtn.addEventListener('click', () => {
            this.hideDuplicateOptions();
        });

        // Listen for tab updates from main process
        window.electronAPI.onTabsUpdated((tabs) => {
            this.tabs = tabs;
            this.filterAndDisplayTabs();
            this.updateDuplicateCount();
        });
    }

    async loadTabs() {
        this.showLoading(true);
        try {
            this.tabs = await window.electronAPI.refreshTabs();
            this.filterAndDisplayTabs();
        } catch (error) {
            console.error('Error loading tabs:', error);
            this.showError('Failed to load tabs');
        } finally {
            this.showLoading(false);
        }
    }

    filterAndDisplayTabs() {
        let filtered = [...this.tabs];

        // Apply browser filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(tab => tab.browser === this.currentFilter);
        }

        // Apply search filter
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(tab => 
                tab.title.toLowerCase().includes(query) ||
                tab.url.toLowerCase().includes(query)
            );
        }

        // Apply sorting
        filtered.sort((a, b) => {
            switch (this.currentSort) {
                case 'title':
                    return a.title.localeCompare(b.title);
                case 'title-desc':
                    return b.title.localeCompare(a.title);
                case 'url':
                    return a.url.localeCompare(b.url);
                case 'url-desc':
                    return b.url.localeCompare(a.url);
                case 'browser':
                    return a.browser.localeCompare(b.browser);
                default:
                    return 0;
            }
        });

        this.filteredTabs = filtered;
        this.displayTabs();
        this.updateStats();
    }

    displayTabs() {
        if (this.filteredTabs.length === 0) {
            this.tabsList.style.display = 'none';
            this.emptyState.style.display = 'block';
            return;
        }

        this.tabsList.style.display = 'block';
        this.emptyState.style.display = 'none';

        this.tabsList.innerHTML = this.filteredTabs.map(tab => this.createTabElement(tab)).join('');

        // Bind close button events
        this.tabsList.querySelectorAll('.tab-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const tabId = btn.dataset.tabId;
                const browserType = btn.dataset.browser;
                this.closeTab(tabId, browserType);
            });
        });

        // Bind tab click events (optional: focus tab)
        this.tabsList.querySelectorAll('.tab-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('tab-close')) {
                    // Could implement tab focusing here
                    console.log('Tab clicked:', item.dataset.tabId);
                }
            });
        });
    }

    createTabElement(tab) {
        const favicon = tab.favicon || this.getBrowserIcon(tab.browser);
        const truncatedTitle = tab.title.length > 60 ? tab.title.substring(0, 60) + '...' : tab.title;
        const truncatedUrl = tab.url.length > 80 ? tab.url.substring(0, 80) + '...' : tab.url;

        return `
            <div class="tab-item" data-tab-id="${tab.id}">
                <div class="tab-favicon">
                    ${favicon ? `<img src="${favicon}" alt="" width="16" height="16">` : this.getBrowserIcon(tab.browser)}
                </div>
                <div class="tab-info">
                    <div class="tab-title" title="${tab.title}">${truncatedTitle}</div>
                    <div class="tab-url" title="${tab.url}">${truncatedUrl}</div>
                </div>
                <div class="tab-browser ${tab.browser}">${tab.browser}</div>
                <button class="tab-close" data-tab-id="${tab.id}" data-browser="${tab.browser}" title="Close tab">
                    ✕
                </button>
            </div>
        `;
    }

    getBrowserIcon(browser) {
        switch (browser) {
            case 'chrome':
                return '🌐';
            case 'edge':
                return '🔷';
            default:
                return '📄';
        }
    }

    async closeTab(tabId, browserType) {
        try {
            await window.electronAPI.closeTab(tabId, browserType);
            // Tab list will be updated via the onTabsUpdated listener
        } catch (error) {
            console.error('Error closing tab:', error);
            this.showError('Failed to close tab');
        }
    }

    updateStats() {
        const totalTabs = this.tabs.length;
        const filteredCount = this.filteredTabs.length;
        
        if (this.currentFilter === 'all' && !this.searchQuery) {
            this.tabCount.textContent = `${totalTabs} tabs`;
        } else {
            this.tabCount.textContent = `${filteredCount} of ${totalTabs} tabs`;
        }
    }

    showLoading(show) {
        this.loadingIndicator.style.display = show ? 'flex' : 'none';
        this.tabsList.style.display = show ? 'none' : 'block';
    }

    showError(message) {
        // Could implement a toast notification system here
        console.error(message);
    }

    async findDuplicates() {
        try {
            this.duplicates = await window.electronAPI.findDuplicates();
            this.updateDuplicateCount();
            this.showingDuplicates = true;
            this.filterAndDisplayTabs();

            if (this.duplicates.length > 0) {
                this.showStatus(`Found ${this.duplicates.length} groups of duplicate URLs`, 'info');
            } else {
                this.showStatus('No duplicate tabs found!', 'success');
            }
        } catch (error) {
            console.error('Error finding duplicates:', error);
            this.showError('Failed to find duplicates');
        }
    }

    updateDuplicateCount() {
        if (this.duplicates.length > 0) {
            const totalDuplicates = this.duplicates.reduce((sum, group) => sum + group.duplicateCount, 0);
            this.duplicateCount.textContent = `${totalDuplicates} duplicates in ${this.duplicates.length} groups`;
            this.duplicateCount.style.display = 'inline';
        } else {
            this.duplicateCount.style.display = 'none';
        }
    }

    showDuplicateOptions() {
        if (this.duplicates.length === 0) {
            this.showError('No duplicates found. Click "Find Duplicates" first.');
            return;
        }
        this.duplicateOptions.style.display = 'block';
    }

    hideDuplicateOptions() {
        this.duplicateOptions.style.display = 'none';
        this.showingDuplicates = false;
        this.filterAndDisplayTabs();
    }

    async previewDuplicateRemoval() {
        try {
            const options = {
                dryRun: true,
                excludePinned: this.excludePinnedCheckbox.checked,
                excludeActive: this.excludeActiveCheckbox.checked
            };

            const result = await window.electronAPI.closeDuplicates(options);
            this.showStatus(`Preview: Would close ${result.totalClosed} duplicate tabs from ${result.duplicateGroups} groups`, 'info');
        } catch (error) {
            console.error('Error previewing duplicate removal:', error);
            this.showError('Failed to preview duplicate removal');
        }
    }

    async closeDuplicates() {
        try {
            const options = {
                dryRun: false,
                excludePinned: this.excludePinnedCheckbox.checked,
                excludeActive: this.excludeActiveCheckbox.checked
            };

            const result = await window.electronAPI.closeDuplicates(options);
            this.showStatus(`Closed ${result.totalClosed} duplicate tabs from ${result.duplicateGroups} groups!`, 'success');

            // Hide options and refresh
            this.hideDuplicateOptions();
            this.duplicates = [];
            this.updateDuplicateCount();

            // Refresh tab list
            setTimeout(() => {
                this.loadTabs();
            }, 1000);

        } catch (error) {
            console.error('Error closing duplicates:', error);
            this.showError('Failed to close duplicate tabs');
        }
    }

    showStatus(message, type) {
        // Reuse existing status display logic
        const status = document.getElementById('status') || this.createStatusElement();
        status.textContent = message;
        status.className = type;

        if (type === 'success') {
            setTimeout(() => {
                status.textContent = '';
                status.className = '';
            }, 3000);
        }
    }

    createStatusElement() {
        const status = document.createElement('div');
        status.id = 'status';
        status.style.cssText = 'position: fixed; top: 20px; right: 20px; padding: 10px; border-radius: 5px; z-index: 1000;';
        document.body.appendChild(status);
        return status;
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TabManagerUI();
});
