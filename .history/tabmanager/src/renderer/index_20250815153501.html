<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Manager</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🗂️ Tab Manager</h1>
            <div class="controls">
                <div class="search-container">
                    <input type="text" id="searchInput" placeholder="Search tabs by title or URL..." />
                    <button id="clearSearch">✕</button>
                </div>
                <button id="refreshBtn" class="btn btn-primary">🔄 Refresh</button>
                <button id="findDuplicatesBtn" class="btn btn-secondary">🔍 Find Duplicates</button>
                <button id="closeDuplicatesBtn" class="btn btn-warning">🗑️ Close Duplicates</button>
            </div>
        </header>

        <div class="stats">
            <span id="tabCount">0 tabs</span>
            <div class="browser-filters">
                <button class="filter-btn active" data-browser="all">All</button>
                <button class="filter-btn" data-browser="chrome">Chrome</button>
                <button class="filter-btn" data-browser="edge">Edge</button>
            </div>
        </div>

        <div class="sort-controls">
            <label>Sort by:</label>
            <select id="sortSelect">
                <option value="title">Title (A-Z)</option>
                <option value="title-desc">Title (Z-A)</option>
                <option value="url">URL (A-Z)</option>
                <option value="url-desc">URL (Z-A)</option>
                <option value="browser">Browser</option>
            </select>
        </div>

        <main class="main-content">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>Loading tabs...</p>
            </div>
            
            <div id="tabsList" class="tabs-list"></div>
            
            <div id="emptyState" class="empty-state" style="display: none;">
                <h3>No tabs found</h3>
                <p>Make sure Chrome or Edge is running with tabs open, or try refreshing.</p>
            </div>
        </main>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
