* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    height: 100vh;
    overflow: hidden;
}

.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.header {
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header h1 {
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 24px;
}

.controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-container input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.search-container input:focus {
    outline: none;
    border-color: #667eea;
}

.search-container button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    color: #999;
    font-size: 16px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.stats {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.browser-filters {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 5px 15px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.sort-controls {
    padding: 10px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sort-controls select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
}

.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #666;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.tabs-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.tab-item {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.tab-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.tab-favicon {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.tab-info {
    flex: 1;
    min-width: 0;
}

.tab-title {
    font-weight: 500;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-url {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.tab-browser {
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

.tab-browser.chrome {
    background: #4285f4;
    color: white;
}

.tab-browser.edge {
    background: #0078d4;
    color: white;
}

.tab-close {
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    font-size: 14px;
}

.tab-close:hover {
    background: #ff3742;
    transform: scale(1.1);
}

.empty-state {
    text-align: center;
    padding: 50px 20px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

/* Scrollbar styling */
.main-content::-webkit-scrollbar {
    width: 8px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.main-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Duplicate management styles */
.duplicate-info {
    color: #ff6b6b;
    font-weight: 500;
    margin-left: 15px;
}

.duplicate-options {
    padding: 20px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    margin: 10px 20px;
}

.duplicate-options h3 {
    margin: 0 0 15px 0;
    color: #856404;
}

.duplicate-options label {
    display: block;
    margin: 10px 0;
    color: #856404;
}

.duplicate-options input[type="checkbox"] {
    margin-right: 8px;
}

.duplicate-options .btn {
    margin-right: 10px;
    margin-top: 10px;
}

.tab-item.duplicate {
    border-left: 4px solid #ff6b6b;
    background: #fff5f5;
}

.tab-item.duplicate-keep {
    border-left: 4px solid #51cf66;
    background: #f3fff3;
}

.duplicate-badge {
    background: #ff6b6b;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    margin-left: 5px;
}
