const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  getTabs: () => ipcRenderer.invoke('get-tabs'),
  refreshTabs: () => ipcRenderer.invoke('refresh-tabs'),
  closeTab: (tabId, browserType) => ipcRenderer.invoke('close-tab', tabId, browserType),
  searchTabs: (query) => ipcRenderer.invoke('search-tabs', query),

  // Duplicate management
  findDuplicates: () => ipcRenderer.invoke('find-duplicates'),
  closeDuplicates: (options) => ipcRenderer.invoke('close-duplicates', options),

  // Listen for tab updates
  onTabsUpdated: (callback) => {
    ipcRenderer.on('tabs-updated', (event, tabs) => callback(tabs));
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  }
});
