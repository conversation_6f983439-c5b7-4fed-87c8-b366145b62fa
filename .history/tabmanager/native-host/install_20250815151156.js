#!/usr/bin/env node

// Installation script for native messaging host
const fs = require('fs');
const path = require('path');
const os = require('os');

class NativeHostInstaller {
  constructor() {
    this.hostName = 'com.tabmanager.host';
    this.hostPath = path.join(__dirname, 'host.js');
    this.platform = os.platform();
  }

  install() {
    console.log('Installing Tab Manager native messaging host...');
    
    try {
      if (this.platform === 'win32') {
        this.installWindows();
      } else if (this.platform === 'darwin') {
        this.installMacOS();
      } else if (this.platform === 'linux') {
        this.installLinux();
      } else {
        throw new Error(`Unsupported platform: ${this.platform}`);
      }
      
      console.log('✅ Native messaging host installed successfully!');
      console.log('\nNext steps:');
      console.log('1. Install the browser extensions from the extensions/ folder');
      console.log('2. Run the Tab Manager app with: npm start');
      
    } catch (error) {
      console.error('❌ Installation failed:', error.message);
      process.exit(1);
    }
  }

  installWindows() {
    const registryPath = `HKEY_CURRENT_USER\\SOFTWARE\\Google\\Chrome\\NativeMessagingHosts\\${this.hostName}`;
    const edgeRegistryPath = `HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Edge\\NativeMessagingHosts\\${this.hostName}`;
    
    // Create manifest
    const manifest = this.createManifest();
    const manifestPath = path.join(__dirname, `${this.hostName}.json`);
    fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
    
    console.log('Created manifest at:', manifestPath);
    console.log('\nTo complete installation on Windows, run these commands as Administrator:');
    console.log(`reg add "${registryPath}" /ve /t REG_SZ /d "${manifestPath}" /f`);
    console.log(`reg add "${edgeRegistryPath}" /ve /t REG_SZ /d "${manifestPath}" /f`);
  }

  installMacOS() {
    // Chrome
    const chromeDir = path.join(os.homedir(), 'Library/Application Support/Google/Chrome/NativeMessagingHosts');
    const chromeManifestPath = path.join(chromeDir, `${this.hostName}.json`);
    
    // Edge
    const edgeDir = path.join(os.homedir(), 'Library/Application Support/Microsoft Edge/NativeMessagingHosts');
    const edgeManifestPath = path.join(edgeDir, `${this.hostName}.json`);
    
    // Create directories if they don't exist
    if (!fs.existsSync(chromeDir)) {
      fs.mkdirSync(chromeDir, { recursive: true });
    }
    if (!fs.existsSync(edgeDir)) {
      fs.mkdirSync(edgeDir, { recursive: true });
    }
    
    // Create and write manifests
    const manifest = this.createManifest();
    fs.writeFileSync(chromeManifestPath, JSON.stringify(manifest, null, 2));
    fs.writeFileSync(edgeManifestPath, JSON.stringify(manifest, null, 2));
    
    // Make host script executable
    fs.chmodSync(this.hostPath, '755');
    
    console.log('Installed manifests:');
    console.log('- Chrome:', chromeManifestPath);
    console.log('- Edge:', edgeManifestPath);
  }

  installLinux() {
    // Chrome
    const chromeDir = path.join(os.homedir(), '.config/google-chrome/NativeMessagingHosts');
    const chromeManifestPath = path.join(chromeDir, `${this.hostName}.json`);
    
    // Edge (if available on Linux)
    const edgeDir = path.join(os.homedir(), '.config/microsoft-edge/NativeMessagingHosts');
    const edgeManifestPath = path.join(edgeDir, `${this.hostName}.json`);
    
    // Create directories if they don't exist
    if (!fs.existsSync(chromeDir)) {
      fs.mkdirSync(chromeDir, { recursive: true });
    }
    if (!fs.existsSync(edgeDir)) {
      fs.mkdirSync(edgeDir, { recursive: true });
    }
    
    // Create and write manifests
    const manifest = this.createManifest();
    fs.writeFileSync(chromeManifestPath, JSON.stringify(manifest, null, 2));
    fs.writeFileSync(edgeManifestPath, JSON.stringify(manifest, null, 2));
    
    // Make host script executable
    fs.chmodSync(this.hostPath, '755');
    
    console.log('Installed manifests:');
    console.log('- Chrome:', chromeManifestPath);
    console.log('- Edge:', edgeManifestPath);
  }

  createManifest() {
    return {
      name: this.hostName,
      description: 'Tab Manager native messaging host',
      path: this.hostPath,
      type: 'stdio',
      allowed_origins: [
        `chrome-extension://${this.getExtensionId('chrome')}/`,
        `extension://${this.getExtensionId('edge')}/`
      ]
    };
  }

  getExtensionId(browser) {
    // TODO: Replace these with your actual extension IDs from Step 3
    // You can find them in chrome://extensions/ or edge://extensions/
    return browser === 'chrome'
      ? 'imdmafbemdhojhedeldkhdhjafgpibkk'  // Replace with actual Chrome extension ID
      : 'REPLACE_WITH_YOUR_EDGE_EXTENSION_ID';   // Replace with actual Edge extension ID
  }

  uninstall() {
    console.log('Uninstalling Tab Manager native messaging host...');
    
    try {
      if (this.platform === 'win32') {
        console.log('To uninstall on Windows, run these commands as Administrator:');
        console.log(`reg delete "HKEY_CURRENT_USER\\SOFTWARE\\Google\\Chrome\\NativeMessagingHosts\\${this.hostName}" /f`);
        console.log(`reg delete "HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Edge\\NativeMessagingHosts\\${this.hostName}" /f`);
      } else {
        const paths = [];
        
        if (this.platform === 'darwin') {
          paths.push(
            path.join(os.homedir(), 'Library/Application Support/Google/Chrome/NativeMessagingHosts', `${this.hostName}.json`),
            path.join(os.homedir(), 'Library/Application Support/Microsoft Edge/NativeMessagingHosts', `${this.hostName}.json`)
          );
        } else if (this.platform === 'linux') {
          paths.push(
            path.join(os.homedir(), '.config/google-chrome/NativeMessagingHosts', `${this.hostName}.json`),
            path.join(os.homedir(), '.config/microsoft-edge/NativeMessagingHosts', `${this.hostName}.json`)
          );
        }
        
        paths.forEach(manifestPath => {
          if (fs.existsSync(manifestPath)) {
            fs.unlinkSync(manifestPath);
            console.log('Removed:', manifestPath);
          }
        });
      }
      
      console.log('✅ Native messaging host uninstalled successfully!');
      
    } catch (error) {
      console.error('❌ Uninstallation failed:', error.message);
      process.exit(1);
    }
  }
}

// Command line interface
const command = process.argv[2];
const installer = new NativeHostInstaller();

switch (command) {
  case 'install':
    installer.install();
    break;
  case 'uninstall':
    installer.uninstall();
    break;
  default:
    console.log('Usage: node install.js [install|uninstall]');
    process.exit(1);
}
