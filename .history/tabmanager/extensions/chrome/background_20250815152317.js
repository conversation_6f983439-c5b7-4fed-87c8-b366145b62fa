// Background script for Tab Manager Bridge extension
class TabManagerBridge {
  constructor() {
    this.nativePort = null;
    this.isConnected = false;
    this.setupNativeMessaging();
    this.setupTabListeners();
  }

  setupNativeMessaging() {
    try {
      // Connect to native messaging host
      this.nativePort = chrome.runtime.connectNative('com.tabmanager.host');
      
      this.nativePort.onMessage.addListener((message) => {
        this.handleNativeMessage(message);
      });

      this.nativePort.onDisconnect.addListener(() => {
        const error = chrome.runtime.lastError;
        console.log('Native messaging disconnected:', error ? error.message : 'No error');
        this.isConnected = false;
        this.nativePort = null;

        // Try to reconnect after a delay, but not too aggressively
        setTimeout(() => {
          console.log('Attempting to reconnect to native host...');
          this.setupNativeMessaging();
        }, 3000);
      });

      this.isConnected = true;
      console.log('Native messaging connected');
      
      // Send initial tab data
      this.sendAllTabs();
      
    } catch (error) {
      console.error('Failed to connect to native messaging:', error);
      this.isConnected = false;
    }
  }

  setupTabListeners() {
    // Listen for tab changes
    chrome.tabs.onCreated.addListener(() => {
      this.sendAllTabs();
    });

    chrome.tabs.onRemoved.addListener(() => {
      this.sendAllTabs();
    });

    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      if (changeInfo.title || changeInfo.url) {
        this.sendAllTabs();
      }
    });

    chrome.windows.onCreated.addListener(() => {
      this.sendAllTabs();
    });

    chrome.windows.onRemoved.addListener(() => {
      this.sendAllTabs();
    });
  }

  async handleNativeMessage(message) {
    console.log('Received native message:', message);
    
    switch (message.action) {
      case 'getTabs':
        await this.sendAllTabs();
        break;
        
      case 'closeTab':
        await this.closeTab(message.tabId);
        break;
        
      case 'focusTab':
        await this.focusTab(message.tabId);
        break;
        
      default:
        console.warn('Unknown action:', message.action);
    }
  }

  async sendAllTabs() {
    if (!this.isConnected || !this.nativePort) {
      return;
    }

    try {
      const tabs = await chrome.tabs.query({});
      const tabData = tabs.map(tab => ({
        id: `chrome-${tab.id}`,
        originalId: tab.id,
        title: tab.title || 'Untitled',
        url: tab.url || '',
        favicon: tab.favIconUrl || '',
        browser: 'chrome',
        windowId: tab.windowId,
        active: tab.active,
        pinned: tab.pinned
      }));

      const message = {
        action: 'tabsUpdate',
        browser: 'chrome',
        tabs: tabData
      };

      this.nativePort.postMessage(message);
      console.log(`Sent ${tabData.length} Chrome tabs to native host`);
      
    } catch (error) {
      console.error('Error sending tabs:', error);
    }
  }

  async closeTab(tabId) {
    try {
      // Extract the original Chrome tab ID
      const originalId = parseInt(tabId.replace('chrome-', ''));
      await chrome.tabs.remove(originalId);
      console.log(`Closed tab ${originalId}`);
      
      // Send updated tab list
      this.sendAllTabs();
      
    } catch (error) {
      console.error('Error closing tab:', error);
    }
  }

  async focusTab(tabId) {
    try {
      const originalId = parseInt(tabId.replace('chrome-', ''));
      const tab = await chrome.tabs.get(originalId);
      
      // Focus the window and activate the tab
      await chrome.windows.update(tab.windowId, { focused: true });
      await chrome.tabs.update(originalId, { active: true });
      
      console.log(`Focused tab ${originalId}`);
      
    } catch (error) {
      console.error('Error focusing tab:', error);
    }
  }
}

// Initialize the bridge when the extension starts
const tabManagerBridge = new TabManagerBridge();

// Handle extension icon click
chrome.action.onClicked.addListener(() => {
  // Could open the desktop app or show status
  console.log('Tab Manager Bridge clicked');
});

// Handle messages from popup
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Received message from popup:', message);

  switch (message.action) {
    case 'getStatus':
      sendResponse({ connected: tabManagerBridge.isConnected });
      break;

    case 'refresh':
      tabManagerBridge.sendAllTabs();
      sendResponse({ success: true });
      break;

    default:
      sendResponse({ error: 'Unknown action' });
  }

  return true; // Keep the message channel open for async response
});
